# .NET with Mahesh - .NET MAUI Learning Platform

A comprehensive learning platform dedicated to .NET MAUI development, built with HTML, CSS, and JavaScript. This website provides tutorials, resources, and hands-on learning experiences for developers looking to master cross-platform application development with .NET MAUI.

## 🌟 Features

### Core Functionality
- **Responsive Design**: Mobile-first approach that works seamlessly across all devices
- **User Authentication**: Registration, login, and session management
- **Content Gating**: Premium content access control for registered users
- **Search & Filter**: Advanced search functionality across tutorials and content
- **Progress Tracking**: User learning progress and completion tracking
- **Bookmarking**: Save favorite tutorials and resources
- **Newsletter Subscription**: Stay updated with latest content

### Content Management
- **Tutorial Catalog**: Comprehensive library of .NET MAUI tutorials
- **Difficulty Levels**: Beginner, Intermediate, and Advanced content
- **Category Organization**: Content organized by topics (Basics, Architecture, UI, etc.)
- **Video Integration**: Embedded video tutorials with chapter navigation
- **Code Samples**: GitHub integration for downloadable code examples
- **Interactive Elements**: Hands-on exercises and practical examples

### User Experience
- **Dark/Light Theme**: Toggle between themes with system preference detection
- **Accessibility**: WCAG compliant with keyboard navigation and screen reader support
- **Performance Optimized**: Fast loading with lazy loading and optimized assets
- **SEO Friendly**: Structured data, meta tags, and semantic HTML
- **Social Sharing**: Share tutorials and content across social platforms

## 🚀 Getting Started

### Prerequisites
- Modern web browser (Chrome, Firefox, Safari, Edge)
- Local web server (optional, for development)

### Installation

1. **Clone or download the repository**
   ```bash
   git clone https://github.com/maheshgadhave/dotnetwithmahesh-website.git
   cd dotnetwithmahesh-website
   ```

2. **Serve the files**
   
   **Option A: Using Python (if installed)**
   ```bash
   # Python 3
   python -m http.server 8000
   
   # Python 2
   python -m SimpleHTTPServer 8000
   ```
   
   **Option B: Using Node.js (if installed)**
   ```bash
   npx serve .
   ```
   
   **Option C: Using PHP (if installed)**
   ```bash
   php -S localhost:8000
   ```
   
   **Option D: Open directly in browser**
   Simply open `index.html` in your web browser

3. **Access the website**
   Open your browser and navigate to `http://localhost:8000` (or the appropriate URL)

## 📁 Project Structure

```
dotnetwithmahesh-website/
├── index.html              # Homepage
├── login.html              # User login page
├── contact.html            # Contact form and information
├── tutorials.html          # Tutorial listing page (to be created)
├── register.html           # User registration page (to be created)
├── styles/
│   ├── main.css            # Core styles and CSS variables
│   ├── components.css      # Component-specific styles
│   └── responsive.css      # Responsive design and media queries
├── js/
│   ├── main.js             # Core application functionality
│   ├── auth.js             # Authentication system
│   ├── search.js           # Search functionality
│   ├── tutorials.js        # Tutorial management
│   ├── contact.js          # Contact form handling
│   ├── login.js            # Login page specific features
│   └── utils.js            # Utility functions
├── data/
│   └── tutorials.json      # Tutorial data structure
├── assets/
│   ├── images/             # Images and graphics
│   ├── icons/              # Icons and favicons
│   └── videos/             # Video thumbnails
└── README.md               # Project documentation
```

## 🎨 Design System

### Color Palette
- **Primary**: #512BD4 (.NET Purple)
- **Secondary**: #10B981 (Success Green)
- **Accent**: #F59E0B (Warning Orange)
- **Neutral**: Gray scale from #F9FAFB to #111827

### Typography
- **Primary Font**: Inter (Sans-serif)
- **Code Font**: JetBrains Mono (Monospace)
- **Font Sizes**: Responsive scale from 0.75rem to 3.75rem

### Components
- **Buttons**: Primary, outline, and text variants
- **Forms**: Floating labels with validation
- **Cards**: Tutorial cards, feature cards, testimonials
- **Navigation**: Fixed header with mobile hamburger menu
- **Modals**: Success messages, premium content gates

## 🔧 Configuration

### Demo Accounts
For testing purposes, use these demo credentials:

**Premium User:**
- Email: `<EMAIL>`
- Password: `demo123`

**Regular User:**
- Email: `<EMAIL>`
- Password: `password`

### Environment Variables
The application uses localStorage for demo purposes. In a production environment, you would configure:

- API endpoints for authentication
- Database connections
- Email service configuration
- Payment processing (for premium subscriptions)
- Analytics tracking codes

## 🌐 Browser Support

- **Chrome**: 90+
- **Firefox**: 88+
- **Safari**: 14+
- **Edge**: 90+
- **Mobile browsers**: iOS Safari 14+, Chrome Mobile 90+

## 📱 Responsive Breakpoints

- **Mobile**: 320px - 639px
- **Tablet**: 640px - 1023px
- **Desktop**: 1024px - 1399px
- **Large Desktop**: 1400px+

## 🔒 Security Features

- **Input Validation**: Client-side validation with server-side verification
- **XSS Protection**: HTML escaping and content sanitization
- **CSRF Protection**: Token-based protection for forms
- **Secure Headers**: Content Security Policy and other security headers
- **Session Management**: Secure session handling with timeout

## 🚀 Performance Optimizations

- **Lazy Loading**: Images and content loaded on demand
- **Code Splitting**: JavaScript modules loaded as needed
- **Caching**: Browser caching with appropriate cache headers
- **Minification**: CSS and JavaScript minification for production
- **Image Optimization**: WebP format with fallbacks
- **CDN Integration**: Font Awesome and Google Fonts from CDN

## 📊 Analytics & Tracking

The platform includes tracking for:
- Page views and user engagement
- Tutorial completion rates
- Search queries and popular content
- User registration and conversion funnels
- Contact form submissions

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Guidelines
- Follow semantic HTML structure
- Use CSS custom properties for theming
- Write accessible code with ARIA labels
- Test across multiple browsers and devices
- Optimize for performance and SEO

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 👨‍💻 Author

**Mahesh Gadhave**
- LinkedIn: [mahesh-gadhave-xamarin-maui-developer](https://www.linkedin.com/in/mahesh-gadhave-xamarin-maui-developer/)
- GitHub: [@maheshgadhave](https://github.com/maheshgadhave)
- Email: <EMAIL>

## 🙏 Acknowledgments

- .NET MAUI team for the amazing framework
- Microsoft for comprehensive documentation
- Open source community for inspiration and resources
- Font Awesome for icons
- Google Fonts for typography

## 📞 Support

For support, email <EMAIL> or create an issue in the GitHub repository.

---

**Built with ❤️ for the .NET MAUI community**
