// Main JavaScript functionality for .NET with Mahesh website

class DotNetWithMaheshApp {
    constructor() {
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupThemeToggle();
        this.setupMobileMenu();
        this.setupSearch();
        this.setupScrollEffects();
        this.setupNewsletterForm();
        this.loadTutorials();
        this.setupAnimations();
    }

    setupEventListeners() {
        // DOM Content Loaded
        document.addEventListener('DOMContentLoaded', () => {
            this.hideLoader();
            this.setupLazyLoading();
        });

        // Window events
        window.addEventListener('scroll', this.throttle(this.handleScroll.bind(this), 16));
        window.addEventListener('resize', this.debounce(this.handleResize.bind(this), 250));
    }

    setupThemeToggle() {
        const themeToggle = document.getElementById('themeToggle');
        const currentTheme = localStorage.getItem('theme') || 'light';
        
        // Set initial theme
        document.documentElement.setAttribute('data-theme', currentTheme);
        this.updateThemeIcon(currentTheme);

        if (themeToggle) {
            themeToggle.addEventListener('click', () => {
                const currentTheme = document.documentElement.getAttribute('data-theme');
                const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
                
                document.documentElement.setAttribute('data-theme', newTheme);
                localStorage.setItem('theme', newTheme);
                this.updateThemeIcon(newTheme);
            });
        }
    }

    updateThemeIcon(theme) {
        const themeToggle = document.getElementById('themeToggle');
        if (themeToggle) {
            const icon = themeToggle.querySelector('i');
            if (icon) {
                icon.className = theme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
            }
        }
    }

    setupMobileMenu() {
        const mobileMenuToggle = document.getElementById('mobileMenuToggle');
        const navMenu = document.getElementById('navMenu');

        if (mobileMenuToggle && navMenu) {
            mobileMenuToggle.addEventListener('click', () => {
                mobileMenuToggle.classList.toggle('active');
                navMenu.classList.toggle('active');
                
                // Prevent body scroll when menu is open
                document.body.style.overflow = navMenu.classList.contains('active') ? 'hidden' : '';
            });

            // Close menu when clicking on nav links
            const navLinks = navMenu.querySelectorAll('.nav-link');
            navLinks.forEach(link => {
                link.addEventListener('click', () => {
                    mobileMenuToggle.classList.remove('active');
                    navMenu.classList.remove('active');
                    document.body.style.overflow = '';
                });
            });
        }
    }

    setupSearch() {
        const searchBtn = document.getElementById('searchBtn');
        const searchOverlay = document.getElementById('searchOverlay');
        const searchClose = document.getElementById('searchClose');
        const searchInput = document.getElementById('searchInput');

        if (searchBtn && searchOverlay) {
            searchBtn.addEventListener('click', () => {
                searchOverlay.classList.add('active');
                if (searchInput) {
                    setTimeout(() => searchInput.focus(), 100);
                }
            });
        }

        if (searchClose && searchOverlay) {
            searchClose.addEventListener('click', () => {
                searchOverlay.classList.remove('active');
            });
        }

        // Close search on escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && searchOverlay && searchOverlay.classList.contains('active')) {
                searchOverlay.classList.remove('active');
            }
        });

        // Close search when clicking outside
        if (searchOverlay) {
            searchOverlay.addEventListener('click', (e) => {
                if (e.target === searchOverlay) {
                    searchOverlay.classList.remove('active');
                }
            });
        }
    }

    setupScrollEffects() {
        // Header scroll effect
        const header = document.querySelector('.header');
        if (header) {
            this.handleScroll();
        }

        // Smooth scroll for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', (e) => {
                e.preventDefault();
                const target = document.querySelector(anchor.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    }

    handleScroll() {
        const header = document.querySelector('.header');
        if (header) {
            const scrolled = window.scrollY > 50;
            header.style.backgroundColor = scrolled 
                ? 'rgba(255, 255, 255, 0.98)' 
                : 'rgba(255, 255, 255, 0.95)';
            header.style.boxShadow = scrolled 
                ? '0 4px 6px -1px rgba(0, 0, 0, 0.1)' 
                : 'none';
        }

        // Animate elements on scroll
        this.animateOnScroll();
    }

    animateOnScroll() {
        const elements = document.querySelectorAll('.animate-on-scroll');
        elements.forEach(element => {
            const elementTop = element.getBoundingClientRect().top;
            const elementVisible = 150;

            if (elementTop < window.innerHeight - elementVisible) {
                element.classList.add('animate-slide-up');
            }
        });
    }

    setupNewsletterForm() {
        const newsletterForm = document.getElementById('newsletterForm');
        if (newsletterForm) {
            newsletterForm.addEventListener('submit', async (e) => {
                e.preventDefault();
                
                const email = document.getElementById('newsletterEmail').value;
                const submitBtn = newsletterForm.querySelector('button[type="submit"]');
                
                if (submitBtn) {
                    const originalText = submitBtn.innerHTML;
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Subscribing...';
                    submitBtn.disabled = true;

                    try {
                        // Simulate API call
                        await this.delay(2000);
                        
                        // Show success message
                        this.showNotification('Successfully subscribed to newsletter!', 'success');
                        newsletterForm.reset();
                    } catch (error) {
                        this.showNotification('Failed to subscribe. Please try again.', 'error');
                    } finally {
                        submitBtn.innerHTML = originalText;
                        submitBtn.disabled = false;
                    }
                }
            });
        }
    }

    async loadTutorials() {
        try {
            // Load tutorial data (in a real app, this would be an API call)
            const tutorials = await this.fetchTutorials();
            this.renderTutorials(tutorials);
            this.setupTutorialFilters(tutorials);
        } catch (error) {
            console.error('Failed to load tutorials:', error);
        }
    }

    async fetchTutorials() {
        // Simulate API call with sample data
        await this.delay(500);
        
        return [
            {
                id: 1,
                title: "Getting Started with .NET MAUI",
                description: "Learn the basics of .NET MAUI and create your first cross-platform application.",
                level: "beginner",
                duration: "45 min",
                image: "assets/tutorials/getting-started.jpg",
                premium: false,
                category: "basics"
            },
            {
                id: 2,
                title: "MVVM Pattern in .NET MAUI",
                description: "Master the Model-View-ViewModel pattern for clean, maintainable code.",
                level: "intermediate",
                duration: "60 min",
                image: "assets/tutorials/mvvm-pattern.jpg",
                premium: true,
                category: "architecture"
            },
            {
                id: 3,
                title: "Custom Controls and Renderers",
                description: "Create custom controls and platform-specific renderers for advanced UI.",
                level: "advanced",
                duration: "90 min",
                image: "assets/tutorials/custom-controls.jpg",
                premium: true,
                category: "ui"
            },
            {
                id: 4,
                title: "Data Binding Fundamentals",
                description: "Understand data binding concepts and implement them in your MAUI apps.",
                level: "beginner",
                duration: "30 min",
                image: "assets/tutorials/data-binding.jpg",
                premium: false,
                category: "basics"
            },
            {
                id: 5,
                title: "Navigation and Shell",
                description: "Implement complex navigation patterns using .NET MAUI Shell.",
                level: "intermediate",
                duration: "75 min",
                image: "assets/tutorials/navigation-shell.jpg",
                premium: true,
                category: "navigation"
            },
            {
                id: 6,
                title: "Performance Optimization",
                description: "Optimize your .NET MAUI apps for better performance and user experience.",
                level: "advanced",
                duration: "120 min",
                image: "assets/tutorials/performance.jpg",
                premium: true,
                category: "performance"
            }
        ];
    }

    renderTutorials(tutorials) {
        const tutorialGrid = document.getElementById('tutorialGrid');
        if (!tutorialGrid) return;

        tutorialGrid.innerHTML = tutorials.map(tutorial => `
            <div class="tutorial-card" data-level="${tutorial.level}" data-category="${tutorial.category}">
                <img src="${tutorial.image}" alt="${tutorial.title}" class="tutorial-image" loading="lazy">
                <div class="tutorial-content">
                    <div class="tutorial-meta">
                        <span class="tutorial-level ${tutorial.level}">${tutorial.level}</span>
                        <span class="tutorial-duration">
                            <i class="fas fa-clock"></i>
                            ${tutorial.duration}
                        </span>
                    </div>
                    <h3 class="tutorial-title">${tutorial.title}</h3>
                    <p class="tutorial-description">${tutorial.description}</p>
                    <div class="tutorial-footer">
                        <a href="tutorial-${tutorial.id}.html" class="tutorial-link">
                            Start Tutorial
                            <i class="fas fa-arrow-right"></i>
                        </a>
                        ${tutorial.premium ? '<span class="tutorial-premium"><i class="fas fa-crown"></i> Premium</span>' : ''}
                    </div>
                </div>
            </div>
        `).join('');
    }

    setupTutorialFilters(tutorials) {
        const filterBtns = document.querySelectorAll('.filter-btn');
        const tutorialCards = document.querySelectorAll('.tutorial-card');

        filterBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                const filter = btn.dataset.filter;
                
                // Update active filter
                filterBtns.forEach(b => b.classList.remove('active'));
                btn.classList.add('active');

                // Filter tutorials
                tutorialCards.forEach(card => {
                    const level = card.dataset.level;
                    const shouldShow = filter === 'all' || level === filter;
                    
                    card.style.display = shouldShow ? 'flex' : 'none';
                    
                    if (shouldShow) {
                        card.style.animation = 'fadeIn 0.5s ease-in-out';
                    }
                });
            });
        });
    }

    setupAnimations() {
        // Add scroll animation class to elements
        const animatedElements = document.querySelectorAll('.section-header, .tutorial-card, .path-card');
        animatedElements.forEach(element => {
            element.classList.add('animate-on-scroll');
        });

        // Initial animation check
        this.animateOnScroll();
    }

    setupLazyLoading() {
        const images = document.querySelectorAll('img[loading="lazy"]');
        
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src || img.src;
                        img.classList.remove('loading');
                        observer.unobserve(img);
                    }
                });
            });

            images.forEach(img => imageObserver.observe(img));
        }
    }

    handleResize() {
        // Handle responsive adjustments
        const mobileMenuToggle = document.getElementById('mobileMenuToggle');
        const navMenu = document.getElementById('navMenu');
        
        if (window.innerWidth > 640) {
            if (mobileMenuToggle) mobileMenuToggle.classList.remove('active');
            if (navMenu) navMenu.classList.remove('active');
            document.body.style.overflow = '';
        }
    }

    hideLoader() {
        const loader = document.querySelector('.loader');
        if (loader) {
            loader.style.opacity = '0';
            setTimeout(() => loader.remove(), 300);
        }
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
                <span>${message}</span>
            </div>
            <button class="notification-close">
                <i class="fas fa-times"></i>
            </button>
        `;

        document.body.appendChild(notification);

        // Auto remove after 5 seconds
        setTimeout(() => {
            notification.style.opacity = '0';
            setTimeout(() => notification.remove(), 300);
        }, 5000);

        // Manual close
        notification.querySelector('.notification-close').addEventListener('click', () => {
            notification.style.opacity = '0';
            setTimeout(() => notification.remove(), 300);
        });
    }

    // Utility functions
    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }

    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// Initialize the application
const app = new DotNetWithMaheshApp();
