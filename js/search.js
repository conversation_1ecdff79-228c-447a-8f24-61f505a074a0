// Search functionality for .NET with Mahesh website

class SearchSystem {
    constructor() {
        this.searchIndex = [];
        this.searchResults = [];
        this.currentQuery = '';
        this.init();
    }

    init() {
        this.buildSearchIndex();
        this.setupEventListeners();
    }

    setupEventListeners() {
        const searchInput = document.getElementById('searchInput');
        const searchSubmit = document.getElementById('searchSubmit');
        const searchResults = document.getElementById('searchResults');

        if (searchInput) {
            // Real-time search as user types
            searchInput.addEventListener('input', this.debounce((e) => {
                const query = e.target.value.trim();
                if (query.length >= 2) {
                    this.performSearch(query);
                } else {
                    this.clearResults();
                }
            }, 300));

            // Handle enter key
            searchInput.addEventListener('keydown', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    this.handleSearchSubmit();
                }
            });
        }

        if (searchSubmit) {
            searchSubmit.addEventListener('click', (e) => {
                e.preventDefault();
                this.handleSearchSubmit();
            });
        }

        // Handle search result clicks
        if (searchResults) {
            searchResults.addEventListener('click', (e) => {
                const resultItem = e.target.closest('.search-result-item');
                if (resultItem) {
                    const url = resultItem.dataset.url;
                    if (url) {
                        window.location.href = url;
                    }
                }
            });
        }
    }

    async buildSearchIndex() {
        try {
            // Build search index from various content sources
            const tutorials = await this.getTutorials();
            const blogPosts = await this.getBlogPosts();
            const resources = await this.getResources();

            this.searchIndex = [
                ...tutorials.map(item => ({ ...item, type: 'tutorial' })),
                ...blogPosts.map(item => ({ ...item, type: 'blog' })),
                ...resources.map(item => ({ ...item, type: 'resource' }))
            ];

            // Add search weights and keywords
            this.searchIndex = this.searchIndex.map(item => ({
                ...item,
                searchText: this.buildSearchText(item),
                keywords: this.extractKeywords(item)
            }));

        } catch (error) {
            console.error('Failed to build search index:', error);
        }
    }

    buildSearchText(item) {
        // Combine all searchable text fields
        const fields = [
            item.title,
            item.description,
            item.content,
            item.tags?.join(' '),
            item.category,
            item.level
        ].filter(Boolean);

        return fields.join(' ').toLowerCase();
    }

    extractKeywords(item) {
        const commonWords = ['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'];
        const text = this.buildSearchText(item);
        
        return text
            .split(/\s+/)
            .filter(word => word.length > 2 && !commonWords.includes(word))
            .slice(0, 10); // Limit to top 10 keywords
    }

    performSearch(query) {
        this.currentQuery = query.toLowerCase();
        
        if (!this.currentQuery) {
            this.clearResults();
            return;
        }

        // Search algorithm
        const results = this.searchIndex
            .map(item => ({
                ...item,
                score: this.calculateRelevanceScore(item, this.currentQuery)
            }))
            .filter(item => item.score > 0)
            .sort((a, b) => b.score - a.score)
            .slice(0, 10); // Limit to top 10 results

        this.searchResults = results;
        this.displayResults(results);
    }

    calculateRelevanceScore(item, query) {
        let score = 0;
        const queryTerms = query.split(/\s+/).filter(term => term.length > 1);

        queryTerms.forEach(term => {
            // Title match (highest weight)
            if (item.title.toLowerCase().includes(term)) {
                score += 10;
                if (item.title.toLowerCase().startsWith(term)) {
                    score += 5; // Bonus for title starting with term
                }
            }

            // Description match
            if (item.description && item.description.toLowerCase().includes(term)) {
                score += 5;
            }

            // Content match
            if (item.content && item.content.toLowerCase().includes(term)) {
                score += 3;
            }

            // Tags match
            if (item.tags && item.tags.some(tag => tag.toLowerCase().includes(term))) {
                score += 4;
            }

            // Category match
            if (item.category && item.category.toLowerCase().includes(term)) {
                score += 3;
            }

            // Level match
            if (item.level && item.level.toLowerCase().includes(term)) {
                score += 2;
            }

            // Keyword match
            if (item.keywords && item.keywords.some(keyword => keyword.includes(term))) {
                score += 1;
            }
        });

        // Boost score for exact phrase matches
        if (item.searchText.includes(query)) {
            score += 15;
        }

        // Boost score for premium content if user is premium
        if (item.premium && window.authSystem && window.authSystem.isPremiumUser()) {
            score += 2;
        }

        return score;
    }

    displayResults(results) {
        const searchResults = document.getElementById('searchResults');
        if (!searchResults) return;

        if (results.length === 0) {
            searchResults.innerHTML = `
                <div class="search-no-results">
                    <i class="fas fa-search"></i>
                    <h3>No results found</h3>
                    <p>Try different keywords or check your spelling</p>
                </div>
            `;
            searchResults.style.display = 'block';
            return;
        }

        const resultsHTML = results.map(result => `
            <div class="search-result-item" data-url="${this.getResultUrl(result)}">
                <div class="search-result-icon">
                    <i class="fas fa-${this.getResultIcon(result.type)}"></i>
                </div>
                <div class="search-result-content">
                    <h4 class="search-result-title">
                        ${this.highlightQuery(result.title, this.currentQuery)}
                        ${result.premium ? '<i class="fas fa-crown premium-icon"></i>' : ''}
                    </h4>
                    <p class="search-result-description">
                        ${this.highlightQuery(this.truncateText(result.description, 120), this.currentQuery)}
                    </p>
                    <div class="search-result-meta">
                        <span class="search-result-type">${this.formatType(result.type)}</span>
                        ${result.level ? `<span class="search-result-level">${result.level}</span>` : ''}
                        ${result.duration ? `<span class="search-result-duration">${result.duration}</span>` : ''}
                    </div>
                </div>
                <div class="search-result-arrow">
                    <i class="fas fa-arrow-right"></i>
                </div>
            </div>
        `).join('');

        searchResults.innerHTML = `
            <div class="search-results-header">
                <h3>Search Results</h3>
                <span class="search-results-count">${results.length} result${results.length !== 1 ? 's' : ''}</span>
            </div>
            <div class="search-results-list">
                ${resultsHTML}
            </div>
        `;

        searchResults.style.display = 'block';
    }

    clearResults() {
        const searchResults = document.getElementById('searchResults');
        if (searchResults) {
            searchResults.style.display = 'none';
            searchResults.innerHTML = '';
        }
    }

    handleSearchSubmit() {
        const searchInput = document.getElementById('searchInput');
        if (searchInput && this.searchResults.length > 0) {
            // Navigate to first result or search results page
            const firstResult = this.searchResults[0];
            const url = this.getResultUrl(firstResult);
            window.location.href = url;
        }
    }

    getResultUrl(result) {
        switch (result.type) {
            case 'tutorial':
                return `tutorial-${result.id}.html`;
            case 'blog':
                return `blog-post-${result.id}.html`;
            case 'resource':
                return `resource-${result.id}.html`;
            default:
                return '#';
        }
    }

    getResultIcon(type) {
        switch (type) {
            case 'tutorial':
                return 'play-circle';
            case 'blog':
                return 'newspaper';
            case 'resource':
                return 'download';
            default:
                return 'file';
        }
    }

    formatType(type) {
        switch (type) {
            case 'tutorial':
                return 'Tutorial';
            case 'blog':
                return 'Blog Post';
            case 'resource':
                return 'Resource';
            default:
                return 'Content';
        }
    }

    highlightQuery(text, query) {
        if (!query || !text) return text;
        
        const queryTerms = query.split(/\s+/).filter(term => term.length > 1);
        let highlightedText = text;
        
        queryTerms.forEach(term => {
            const regex = new RegExp(`(${this.escapeRegex(term)})`, 'gi');
            highlightedText = highlightedText.replace(regex, '<mark>$1</mark>');
        });
        
        return highlightedText;
    }

    truncateText(text, maxLength) {
        if (!text || text.length <= maxLength) return text;
        return text.substring(0, maxLength).trim() + '...';
    }

    escapeRegex(string) {
        return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    }

    // Data fetching methods (simulate API calls)
    async getTutorials() {
        return [
            {
                id: 1,
                title: "Getting Started with .NET MAUI",
                description: "Learn the basics of .NET MAUI and create your first cross-platform application.",
                content: "This comprehensive tutorial covers the fundamentals of .NET MAUI development...",
                level: "beginner",
                duration: "45 min",
                category: "basics",
                tags: ["maui", "getting-started", "cross-platform", "mobile"],
                premium: false
            },
            {
                id: 2,
                title: "MVVM Pattern in .NET MAUI",
                description: "Master the Model-View-ViewModel pattern for clean, maintainable code.",
                content: "Learn how to implement MVVM pattern in your .NET MAUI applications...",
                level: "intermediate",
                duration: "60 min",
                category: "architecture",
                tags: ["mvvm", "architecture", "design-patterns", "data-binding"],
                premium: true
            },
            {
                id: 3,
                title: "Custom Controls and Renderers",
                description: "Create custom controls and platform-specific renderers for advanced UI.",
                content: "Deep dive into creating custom controls and platform renderers...",
                level: "advanced",
                duration: "90 min",
                category: "ui",
                tags: ["custom-controls", "renderers", "ui", "advanced"],
                premium: true
            }
        ];
    }

    async getBlogPosts() {
        return [
            {
                id: 1,
                title: ".NET MAUI vs Xamarin: Migration Guide",
                description: "Complete guide to migrating from Xamarin.Forms to .NET MAUI",
                content: "Detailed comparison and migration strategies...",
                category: "migration",
                tags: ["migration", "xamarin", "maui", "upgrade"],
                premium: false
            },
            {
                id: 2,
                title: "Performance Best Practices for .NET MAUI",
                description: "Tips and techniques to optimize your .NET MAUI app performance",
                content: "Learn about performance optimization techniques...",
                category: "performance",
                tags: ["performance", "optimization", "best-practices"],
                premium: true
            }
        ];
    }

    async getResources() {
        return [
            {
                id: 1,
                title: ".NET MAUI Cheat Sheet",
                description: "Quick reference guide for .NET MAUI development",
                content: "Comprehensive cheat sheet with code snippets...",
                category: "reference",
                tags: ["cheat-sheet", "reference", "quick-guide"],
                premium: false
            },
            {
                id: 2,
                title: "Sample Project Templates",
                description: "Ready-to-use project templates for common scenarios",
                content: "Collection of project templates and starter kits...",
                category: "templates",
                tags: ["templates", "samples", "starter-kits"],
                premium: true
            }
        ];
    }

    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
}

// Initialize search system
const searchSystem = new SearchSystem();

// Make it globally available
window.searchSystem = searchSystem;
