// Tutorial management system for .NET with Mahesh website

class TutorialSystem {
    constructor() {
        this.tutorials = [];
        this.currentFilter = 'all';
        this.currentSort = 'newest';
        this.currentPage = 1;
        this.tutorialsPerPage = 9;
        this.init();
    }

    init() {
        this.loadTutorials();
        this.setupEventListeners();
        this.setupProgressTracking();
    }

    setupEventListeners() {
        // Filter buttons
        document.addEventListener('click', (e) => {
            if (e.target.matches('.filter-btn')) {
                this.handleFilterChange(e.target);
            }
        });

        // Sort dropdown
        const sortSelect = document.getElementById('tutorialSort');
        if (sortSelect) {
            sortSelect.addEventListener('change', (e) => {
                this.currentSort = e.target.value;
                this.renderTutorials();
            });
        }

        // Pagination
        document.addEventListener('click', (e) => {
            if (e.target.matches('.pagination-btn')) {
                const page = parseInt(e.target.dataset.page);
                if (page && page !== this.currentPage) {
                    this.currentPage = page;
                    this.renderTutorials();
                    this.scrollToTop();
                }
            }
        });

        // Tutorial card interactions
        document.addEventListener('click', (e) => {
            if (e.target.matches('.tutorial-bookmark')) {
                e.preventDefault();
                this.toggleBookmark(e.target);
            }
            
            if (e.target.matches('.tutorial-progress')) {
                e.preventDefault();
                this.updateProgress(e.target);
            }
        });

        // Search within tutorials
        const tutorialSearch = document.getElementById('tutorialSearch');
        if (tutorialSearch) {
            tutorialSearch.addEventListener('input', this.debounce((e) => {
                this.searchTutorials(e.target.value);
            }, 300));
        }
    }

    async loadTutorials() {
        try {
            this.tutorials = await this.fetchTutorials();
            this.renderTutorials();
            this.updateStats();
        } catch (error) {
            console.error('Failed to load tutorials:', error);
            this.showError('Failed to load tutorials. Please try again.');
        }
    }

    async fetchTutorials() {
        // Simulate API call with comprehensive tutorial data
        await this.delay(500);
        
        return [
            {
                id: 1,
                title: "Getting Started with .NET MAUI",
                description: "Learn the basics of .NET MAUI and create your first cross-platform application. This tutorial covers project setup, basic UI elements, and deployment.",
                level: "beginner",
                duration: "45 min",
                image: "assets/tutorials/getting-started.jpg",
                premium: false,
                category: "basics",
                tags: ["maui", "getting-started", "cross-platform"],
                publishDate: "2024-01-15",
                views: 1250,
                rating: 4.8,
                completions: 890,
                videoUrl: "https://example.com/video1",
                codeUrl: "https://github.com/example/tutorial1"
            },
            {
                id: 2,
                title: "MVVM Pattern in .NET MAUI",
                description: "Master the Model-View-ViewModel pattern for clean, maintainable code. Learn data binding, commands, and proper separation of concerns.",
                level: "intermediate",
                duration: "60 min",
                image: "assets/tutorials/mvvm-pattern.jpg",
                premium: true,
                category: "architecture",
                tags: ["mvvm", "architecture", "data-binding"],
                publishDate: "2024-01-20",
                views: 980,
                rating: 4.9,
                completions: 650,
                videoUrl: "https://example.com/video2",
                codeUrl: "https://github.com/example/tutorial2"
            },
            {
                id: 3,
                title: "Custom Controls and Renderers",
                description: "Create custom controls and platform-specific renderers for advanced UI scenarios. Build reusable components for your applications.",
                level: "advanced",
                duration: "90 min",
                image: "assets/tutorials/custom-controls.jpg",
                premium: true,
                category: "ui",
                tags: ["custom-controls", "renderers", "ui"],
                publishDate: "2024-01-25",
                views: 720,
                rating: 4.7,
                completions: 420,
                videoUrl: "https://example.com/video3",
                codeUrl: "https://github.com/example/tutorial3"
            },
            {
                id: 4,
                title: "Data Binding Fundamentals",
                description: "Understand data binding concepts and implement them in your MAUI apps. Learn about binding modes, converters, and validation.",
                level: "beginner",
                duration: "30 min",
                image: "assets/tutorials/data-binding.jpg",
                premium: false,
                category: "basics",
                tags: ["data-binding", "fundamentals", "ui"],
                publishDate: "2024-02-01",
                views: 1100,
                rating: 4.6,
                completions: 780,
                videoUrl: "https://example.com/video4",
                codeUrl: "https://github.com/example/tutorial4"
            },
            {
                id: 5,
                title: "Navigation and Shell",
                description: "Implement complex navigation patterns using .NET MAUI Shell. Learn about routes, navigation parameters, and deep linking.",
                level: "intermediate",
                duration: "75 min",
                image: "assets/tutorials/navigation-shell.jpg",
                premium: true,
                category: "navigation",
                tags: ["navigation", "shell", "routing"],
                publishDate: "2024-02-05",
                views: 850,
                rating: 4.8,
                completions: 520,
                videoUrl: "https://example.com/video5",
                codeUrl: "https://github.com/example/tutorial5"
            },
            {
                id: 6,
                title: "Performance Optimization",
                description: "Optimize your .NET MAUI apps for better performance and user experience. Learn about memory management, rendering optimization, and profiling.",
                level: "advanced",
                duration: "120 min",
                image: "assets/tutorials/performance.jpg",
                premium: true,
                category: "performance",
                tags: ["performance", "optimization", "profiling"],
                publishDate: "2024-02-10",
                views: 650,
                rating: 4.9,
                completions: 380,
                videoUrl: "https://example.com/video6",
                codeUrl: "https://github.com/example/tutorial6"
            },
            {
                id: 7,
                title: "Working with APIs and HTTP",
                description: "Learn how to consume REST APIs, handle HTTP requests, and manage data in your .NET MAUI applications.",
                level: "intermediate",
                duration: "55 min",
                image: "assets/tutorials/api-http.jpg",
                premium: false,
                category: "data",
                tags: ["api", "http", "rest", "data"],
                publishDate: "2024-02-15",
                views: 920,
                rating: 4.7,
                completions: 610,
                videoUrl: "https://example.com/video7",
                codeUrl: "https://github.com/example/tutorial7"
            },
            {
                id: 8,
                title: "Local Database with SQLite",
                description: "Implement local data storage using SQLite in .NET MAUI. Learn about Entity Framework Core and data persistence.",
                level: "intermediate",
                duration: "80 min",
                image: "assets/tutorials/sqlite-database.jpg",
                premium: true,
                category: "data",
                tags: ["sqlite", "database", "entity-framework"],
                publishDate: "2024-02-20",
                views: 780,
                rating: 4.8,
                completions: 450,
                videoUrl: "https://example.com/video8",
                codeUrl: "https://github.com/example/tutorial8"
            },
            {
                id: 9,
                title: "Platform-Specific Features",
                description: "Access platform-specific features and APIs in your cross-platform .NET MAUI applications.",
                level: "advanced",
                duration: "95 min",
                image: "assets/tutorials/platform-specific.jpg",
                premium: true,
                category: "platform",
                tags: ["platform-specific", "native", "apis"],
                publishDate: "2024-02-25",
                views: 580,
                rating: 4.6,
                completions: 320,
                videoUrl: "https://example.com/video9",
                codeUrl: "https://github.com/example/tutorial9"
            }
        ];
    }

    renderTutorials() {
        const filteredTutorials = this.getFilteredTutorials();
        const sortedTutorials = this.getSortedTutorials(filteredTutorials);
        const paginatedTutorials = this.getPaginatedTutorials(sortedTutorials);
        
        this.renderTutorialGrid(paginatedTutorials);
        this.renderPagination(sortedTutorials.length);
        this.updateResultsCount(sortedTutorials.length);
    }

    getFilteredTutorials() {
        return this.tutorials.filter(tutorial => {
            if (this.currentFilter === 'all') return true;
            if (this.currentFilter === 'free') return !tutorial.premium;
            if (this.currentFilter === 'premium') return tutorial.premium;
            return tutorial.level === this.currentFilter;
        });
    }

    getSortedTutorials(tutorials) {
        return [...tutorials].sort((a, b) => {
            switch (this.currentSort) {
                case 'newest':
                    return new Date(b.publishDate) - new Date(a.publishDate);
                case 'oldest':
                    return new Date(a.publishDate) - new Date(b.publishDate);
                case 'popular':
                    return b.views - a.views;
                case 'rating':
                    return b.rating - a.rating;
                case 'duration-short':
                    return this.parseDuration(a.duration) - this.parseDuration(b.duration);
                case 'duration-long':
                    return this.parseDuration(b.duration) - this.parseDuration(a.duration);
                default:
                    return 0;
            }
        });
    }

    getPaginatedTutorials(tutorials) {
        const startIndex = (this.currentPage - 1) * this.tutorialsPerPage;
        const endIndex = startIndex + this.tutorialsPerPage;
        return tutorials.slice(startIndex, endIndex);
    }

    renderTutorialGrid(tutorials) {
        const tutorialGrid = document.getElementById('tutorialGrid');
        if (!tutorialGrid) return;

        if (tutorials.length === 0) {
            tutorialGrid.innerHTML = `
                <div class="no-tutorials">
                    <i class="fas fa-search"></i>
                    <h3>No tutorials found</h3>
                    <p>Try adjusting your filters or search terms</p>
                </div>
            `;
            return;
        }

        tutorialGrid.innerHTML = tutorials.map(tutorial => this.createTutorialCard(tutorial)).join('');
    }

    createTutorialCard(tutorial) {
        const isBookmarked = this.isBookmarked(tutorial.id);
        const progress = this.getProgress(tutorial.id);
        const canAccess = !tutorial.premium || (window.authSystem && window.authSystem.isPremiumUser());

        return `
            <div class="tutorial-card ${!canAccess ? 'locked' : ''}" data-tutorial-id="${tutorial.id}">
                <div class="tutorial-image-container">
                    <img src="${tutorial.image}" alt="${tutorial.title}" class="tutorial-image" loading="lazy">
                    <div class="tutorial-overlay">
                        <button class="tutorial-bookmark ${isBookmarked ? 'active' : ''}" data-tutorial-id="${tutorial.id}">
                            <i class="fas fa-bookmark"></i>
                        </button>
                        ${tutorial.premium ? '<div class="premium-badge"><i class="fas fa-crown"></i></div>' : ''}
                    </div>
                    ${progress > 0 ? `<div class="progress-bar"><div class="progress-fill" style="width: ${progress}%"></div></div>` : ''}
                </div>
                <div class="tutorial-content">
                    <div class="tutorial-meta">
                        <span class="tutorial-level ${tutorial.level}">${tutorial.level}</span>
                        <span class="tutorial-duration">
                            <i class="fas fa-clock"></i>
                            ${tutorial.duration}
                        </span>
                        <div class="tutorial-rating">
                            <i class="fas fa-star"></i>
                            <span>${tutorial.rating}</span>
                        </div>
                    </div>
                    <h3 class="tutorial-title">${tutorial.title}</h3>
                    <p class="tutorial-description">${tutorial.description}</p>
                    <div class="tutorial-stats">
                        <span class="tutorial-views">
                            <i class="fas fa-eye"></i>
                            ${this.formatNumber(tutorial.views)} views
                        </span>
                        <span class="tutorial-completions">
                            <i class="fas fa-check-circle"></i>
                            ${this.formatNumber(tutorial.completions)} completed
                        </span>
                    </div>
                    <div class="tutorial-footer">
                        <a href="${canAccess ? `tutorial-${tutorial.id}.html` : '#'}" 
                           class="tutorial-link ${!canAccess ? 'premium-content' : ''}">
                            ${progress > 0 ? 'Continue' : 'Start Tutorial'}
                            <i class="fas fa-arrow-right"></i>
                        </a>
                        <div class="tutorial-actions">
                            <a href="${tutorial.codeUrl}" target="_blank" class="action-btn" title="View Code">
                                <i class="fab fa-github"></i>
                            </a>
                            <button class="action-btn share-btn" data-tutorial-id="${tutorial.id}" title="Share">
                                <i class="fas fa-share-alt"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    renderPagination(totalTutorials) {
        const paginationContainer = document.getElementById('pagination');
        if (!paginationContainer) return;

        const totalPages = Math.ceil(totalTutorials / this.tutorialsPerPage);
        
        if (totalPages <= 1) {
            paginationContainer.innerHTML = '';
            return;
        }

        let paginationHTML = '';
        
        // Previous button
        if (this.currentPage > 1) {
            paginationHTML += `<button class="pagination-btn" data-page="${this.currentPage - 1}">Previous</button>`;
        }

        // Page numbers
        for (let i = 1; i <= totalPages; i++) {
            if (i === this.currentPage) {
                paginationHTML += `<button class="pagination-btn active" data-page="${i}">${i}</button>`;
            } else if (i === 1 || i === totalPages || (i >= this.currentPage - 2 && i <= this.currentPage + 2)) {
                paginationHTML += `<button class="pagination-btn" data-page="${i}">${i}</button>`;
            } else if (i === this.currentPage - 3 || i === this.currentPage + 3) {
                paginationHTML += `<span class="pagination-ellipsis">...</span>`;
            }
        }

        // Next button
        if (this.currentPage < totalPages) {
            paginationHTML += `<button class="pagination-btn" data-page="${this.currentPage + 1}">Next</button>`;
        }

        paginationContainer.innerHTML = paginationHTML;
    }

    handleFilterChange(filterBtn) {
        // Update active filter
        document.querySelectorAll('.filter-btn').forEach(btn => btn.classList.remove('active'));
        filterBtn.classList.add('active');
        
        this.currentFilter = filterBtn.dataset.filter;
        this.currentPage = 1; // Reset to first page
        this.renderTutorials();
    }

    searchTutorials(query) {
        if (!query.trim()) {
            this.renderTutorials();
            return;
        }

        const searchResults = this.tutorials.filter(tutorial => 
            tutorial.title.toLowerCase().includes(query.toLowerCase()) ||
            tutorial.description.toLowerCase().includes(query.toLowerCase()) ||
            tutorial.tags.some(tag => tag.toLowerCase().includes(query.toLowerCase()))
        );

        this.renderTutorialGrid(searchResults);
        this.updateResultsCount(searchResults.length);
        document.getElementById('pagination').innerHTML = '';
    }

    toggleBookmark(button) {
        const tutorialId = parseInt(button.dataset.tutorialId);
        const isBookmarked = button.classList.contains('active');
        
        if (isBookmarked) {
            this.removeBookmark(tutorialId);
            button.classList.remove('active');
        } else {
            this.addBookmark(tutorialId);
            button.classList.add('active');
        }
    }

    addBookmark(tutorialId) {
        const bookmarks = this.getBookmarks();
        if (!bookmarks.includes(tutorialId)) {
            bookmarks.push(tutorialId);
            localStorage.setItem('dotnetWithMahesh_bookmarks', JSON.stringify(bookmarks));
        }
    }

    removeBookmark(tutorialId) {
        const bookmarks = this.getBookmarks();
        const index = bookmarks.indexOf(tutorialId);
        if (index > -1) {
            bookmarks.splice(index, 1);
            localStorage.setItem('dotnetWithMahesh_bookmarks', JSON.stringify(bookmarks));
        }
    }

    getBookmarks() {
        const bookmarks = localStorage.getItem('dotnetWithMahesh_bookmarks');
        return bookmarks ? JSON.parse(bookmarks) : [];
    }

    isBookmarked(tutorialId) {
        return this.getBookmarks().includes(tutorialId);
    }

    getProgress(tutorialId) {
        const progress = localStorage.getItem(`dotnetWithMahesh_progress_${tutorialId}`);
        return progress ? parseInt(progress) : 0;
    }

    updateProgress(tutorialId, progress) {
        localStorage.setItem(`dotnetWithMahesh_progress_${tutorialId}`, progress.toString());
    }

    updateStats() {
        const totalTutorials = this.tutorials.length;
        const freeTutorials = this.tutorials.filter(t => !t.premium).length;
        const premiumTutorials = this.tutorials.filter(t => t.premium).length;

        const statsContainer = document.getElementById('tutorialStats');
        if (statsContainer) {
            statsContainer.innerHTML = `
                <div class="stat-item">
                    <span class="stat-number">${totalTutorials}</span>
                    <span class="stat-label">Total Tutorials</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">${freeTutorials}</span>
                    <span class="stat-label">Free</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">${premiumTutorials}</span>
                    <span class="stat-label">Premium</span>
                </div>
            `;
        }
    }

    updateResultsCount(count) {
        const resultsCount = document.getElementById('resultsCount');
        if (resultsCount) {
            resultsCount.textContent = `${count} tutorial${count !== 1 ? 's' : ''} found`;
        }
    }

    setupProgressTracking() {
        // Track tutorial completion and progress
        window.addEventListener('beforeunload', () => {
            // Save any pending progress updates
            this.saveProgressData();
        });
    }

    saveProgressData() {
        // Implementation for saving progress data
        // This would typically sync with a backend service
    }

    scrollToTop() {
        window.scrollTo({ top: 0, behavior: 'smooth' });
    }

    parseDuration(duration) {
        const match = duration.match(/(\d+)\s*min/);
        return match ? parseInt(match[1]) : 0;
    }

    formatNumber(num) {
        if (num >= 1000) {
            return (num / 1000).toFixed(1) + 'k';
        }
        return num.toString();
    }

    showError(message) {
        if (window.app && window.app.showNotification) {
            window.app.showNotification(message, 'error');
        }
    }

    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// Initialize tutorial system
const tutorialSystem = new TutorialSystem();

// Make it globally available
window.tutorialSystem = tutorialSystem;
