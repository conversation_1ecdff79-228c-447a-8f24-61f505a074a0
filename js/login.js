// Login page specific functionality

class LoginPage {
    constructor() {
        this.init();
    }

    init() {
        this.setupPasswordToggle();
        this.setupDemoCredentials();
        this.setupSocialLogin();
        this.setupFormEnhancements();
        this.checkExistingSession();
    }

    setupPasswordToggle() {
        const passwordToggle = document.getElementById('passwordToggle');
        const passwordInput = document.getElementById('password');

        if (passwordToggle && passwordInput) {
            passwordToggle.addEventListener('click', () => {
                const isPassword = passwordInput.type === 'password';
                passwordInput.type = isPassword ? 'text' : 'password';
                
                const icon = passwordToggle.querySelector('i');
                icon.className = isPassword ? 'fas fa-eye-slash' : 'fas fa-eye';
                
                // Update aria-label for accessibility
                passwordToggle.setAttribute('aria-label', 
                    isPassword ? 'Hide password' : 'Show password'
                );
            });
        }
    }

    setupDemoCredentials() {
        const fillDemoBtn = document.getElementById('fillDemoCredentials');
        const emailInput = document.getElementById('email');
        const passwordInput = document.getElementById('password');

        if (fillDemoBtn && emailInput && passwordInput) {
            fillDemoBtn.addEventListener('click', () => {
                emailInput.value = '<EMAIL>';
                passwordInput.value = 'demo123';
                
                // Add visual feedback
                fillDemoBtn.innerHTML = '<i class="fas fa-check"></i> Demo Credentials Filled';
                fillDemoBtn.classList.add('success');
                
                setTimeout(() => {
                    fillDemoBtn.innerHTML = 'Use Demo Account';
                    fillDemoBtn.classList.remove('success');
                }, 2000);

                // Focus on submit button
                const submitBtn = document.querySelector('.auth-submit');
                if (submitBtn) {
                    submitBtn.focus();
                }
            });
        }
    }

    setupSocialLogin() {
        const socialButtons = document.querySelectorAll('.social-btn');
        
        socialButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                const provider = this.getSocialProvider(button);
                this.handleSocialLogin(provider);
            });
        });
    }

    getSocialProvider(button) {
        if (button.classList.contains('google-btn')) return 'google';
        if (button.classList.contains('github-btn')) return 'github';
        if (button.classList.contains('linkedin-btn')) return 'linkedin';
        return 'unknown';
    }

    async handleSocialLogin(provider) {
        try {
            // Show loading state
            const button = document.querySelector(`.${provider}-btn`);
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Connecting...';
            button.disabled = true;

            // Simulate social login process
            await this.delay(2000);

            // In a real application, this would redirect to the OAuth provider
            this.showNotification(`${this.capitalizeFirst(provider)} login is not implemented in this demo`, 'info');

        } catch (error) {
            console.error('Social login error:', error);
            this.showNotification('Social login failed. Please try again.', 'error');
        } finally {
            // Reset button state
            const button = document.querySelector(`.${provider}-btn`);
            button.innerHTML = `<i class="fab fa-${provider}"></i> ${this.capitalizeFirst(provider)}`;
            button.disabled = false;
        }
    }

    setupFormEnhancements() {
        const form = document.getElementById('loginForm');
        const inputs = form.querySelectorAll('input');

        // Add floating label effect
        inputs.forEach(input => {
            this.setupFloatingLabel(input);
        });

        // Add form validation feedback
        inputs.forEach(input => {
            input.addEventListener('blur', () => this.validateInput(input));
            input.addEventListener('input', () => this.clearInputError(input));
        });

        // Handle Enter key navigation
        inputs.forEach((input, index) => {
            input.addEventListener('keydown', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    const nextInput = inputs[index + 1];
                    if (nextInput) {
                        nextInput.focus();
                    } else {
                        form.querySelector('button[type="submit"]').click();
                    }
                }
            });
        });
    }

    setupFloatingLabel(input) {
        const formGroup = input.closest('.form-group');
        const label = formGroup?.querySelector('.form-label');
        
        if (!label) return;

        // Check if input has value on load
        this.updateFloatingLabel(input, label);

        // Update on focus/blur
        input.addEventListener('focus', () => {
            label.classList.add('floating');
        });

        input.addEventListener('blur', () => {
            this.updateFloatingLabel(input, label);
        });

        input.addEventListener('input', () => {
            this.updateFloatingLabel(input, label);
        });
    }

    updateFloatingLabel(input, label) {
        if (input.value.trim() !== '') {
            label.classList.add('floating');
        } else {
            label.classList.remove('floating');
        }
    }

    validateInput(input) {
        const value = input.value.trim();
        let isValid = true;
        let errorMessage = '';

        // Clear previous errors
        this.clearInputError(input);

        // Email validation
        if (input.type === 'email') {
            if (!value) {
                errorMessage = 'Email is required';
                isValid = false;
            } else if (!this.isValidEmail(value)) {
                errorMessage = 'Please enter a valid email address';
                isValid = false;
            }
        }

        // Password validation
        if (input.type === 'password') {
            if (!value) {
                errorMessage = 'Password is required';
                isValid = false;
            } else if (value.length < 6) {
                errorMessage = 'Password must be at least 6 characters';
                isValid = false;
            }
        }

        if (!isValid) {
            this.showInputError(input, errorMessage);
        }

        return isValid;
    }

    showInputError(input, message) {
        const formGroup = input.closest('.form-group');
        if (formGroup) {
            input.classList.add('error');
            
            // Create error element if it doesn't exist
            let errorElement = formGroup.querySelector('.input-error');
            if (!errorElement) {
                errorElement = document.createElement('div');
                errorElement.className = 'input-error';
                formGroup.appendChild(errorElement);
            }
            
            errorElement.textContent = message;
        }
    }

    clearInputError(input) {
        const formGroup = input.closest('.form-group');
        if (formGroup) {
            input.classList.remove('error');
            const errorElement = formGroup.querySelector('.input-error');
            if (errorElement) {
                errorElement.remove();
            }
        }
    }

    checkExistingSession() {
        // Check if user is already logged in
        if (window.authSystem && window.authSystem.isLoggedIn()) {
            // Redirect to dashboard or intended page
            const redirectUrl = this.getRedirectUrl() || 'index.html';
            this.showNotification('You are already logged in. Redirecting...', 'info');
            
            setTimeout(() => {
                window.location.href = redirectUrl;
            }, 1500);
        }
    }

    getRedirectUrl() {
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get('redirect');
    }

    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    capitalizeFirst(str) {
        return str.charAt(0).toUpperCase() + str.slice(1);
    }

    showNotification(message, type = 'info') {
        // Use the main app's notification system if available
        if (window.app && window.app.showNotification) {
            window.app.showNotification(message, type);
        } else {
            // Fallback notification
            console.log(`${type.toUpperCase()}: ${message}`);
        }
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// Initialize login page functionality
document.addEventListener('DOMContentLoaded', () => {
    const loginPage = new LoginPage();
    window.loginPage = loginPage;
});
