// Authentication system for .NET with Mahesh website

class AuthSystem {
    constructor() {
        this.currentUser = null;
        this.init();
    }

    init() {
        this.loadUserFromStorage();
        this.updateUI();
        this.setupEventListeners();
    }

    setupEventListeners() {
        // Login form
        const loginForm = document.getElementById('loginForm');
        if (loginForm) {
            loginForm.addEventListener('submit', this.handleLogin.bind(this));
        }

        // Register form
        const registerForm = document.getElementById('registerForm');
        if (registerForm) {
            registerForm.addEventListener('submit', this.handleRegister.bind(this));
        }

        // Logout buttons
        document.addEventListener('click', (e) => {
            if (e.target.matches('.logout-btn')) {
                this.handleLogout();
            }
        });

        // Premium content access
        document.addEventListener('click', (e) => {
            if (e.target.matches('.premium-content, .premium-content *')) {
                e.preventDefault();
                this.handlePremiumAccess(e);
            }
        });
    }

    async handleLogin(e) {
        e.preventDefault();
        
        const form = e.target;
        const formData = new FormData(form);
        const email = formData.get('email');
        const password = formData.get('password');
        const submitBtn = form.querySelector('button[type="submit"]');
        
        if (!this.validateEmail(email)) {
            this.showError('Please enter a valid email address');
            return;
        }

        if (!password || password.length < 6) {
            this.showError('Password must be at least 6 characters long');
            return;
        }

        // Show loading state
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Signing In...';
        submitBtn.disabled = true;

        try {
            // Simulate API call
            const user = await this.authenticateUser(email, password);
            
            if (user) {
                this.setCurrentUser(user);
                this.showSuccess('Successfully logged in!');
                
                // Redirect to dashboard or previous page
                const redirectUrl = new URLSearchParams(window.location.search).get('redirect') || 'index.html';
                setTimeout(() => {
                    window.location.href = redirectUrl;
                }, 1500);
            } else {
                this.showError('Invalid email or password');
            }
        } catch (error) {
            this.showError('Login failed. Please try again.');
            console.error('Login error:', error);
        } finally {
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }
    }

    async handleRegister(e) {
        e.preventDefault();
        
        const form = e.target;
        const formData = new FormData(form);
        const firstName = formData.get('firstName');
        const lastName = formData.get('lastName');
        const email = formData.get('email');
        const password = formData.get('password');
        const confirmPassword = formData.get('confirmPassword');
        const agreeTerms = formData.get('agreeTerms');
        const submitBtn = form.querySelector('button[type="submit"]');

        // Validation
        if (!firstName || !lastName) {
            this.showError('Please enter your full name');
            return;
        }

        if (!this.validateEmail(email)) {
            this.showError('Please enter a valid email address');
            return;
        }

        if (!password || password.length < 6) {
            this.showError('Password must be at least 6 characters long');
            return;
        }

        if (password !== confirmPassword) {
            this.showError('Passwords do not match');
            return;
        }

        if (!agreeTerms) {
            this.showError('Please agree to the terms and conditions');
            return;
        }

        // Show loading state
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Creating Account...';
        submitBtn.disabled = true;

        try {
            // Check if user already exists
            const existingUser = await this.checkUserExists(email);
            if (existingUser) {
                this.showError('An account with this email already exists');
                return;
            }

            // Create new user
            const newUser = await this.createUser({
                firstName,
                lastName,
                email,
                password
            });

            if (newUser) {
                this.setCurrentUser(newUser);
                this.showSuccess('Account created successfully! Welcome to .NET with Mahesh!');
                
                // Redirect to welcome page or dashboard
                setTimeout(() => {
                    window.location.href = 'welcome.html';
                }, 1500);
            }
        } catch (error) {
            this.showError('Registration failed. Please try again.');
            console.error('Registration error:', error);
        } finally {
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }
    }

    handleLogout() {
        this.currentUser = null;
        localStorage.removeItem('dotnetWithMahesh_user');
        sessionStorage.removeItem('dotnetWithMahesh_session');
        
        this.updateUI();
        this.showSuccess('Successfully logged out');
        
        // Redirect to home page
        setTimeout(() => {
            window.location.href = 'index.html';
        }, 1000);
    }

    handlePremiumAccess(e) {
        if (!this.currentUser) {
            this.showPremiumModal('Please log in to access premium content');
            return;
        }

        if (!this.currentUser.isPremium) {
            this.showPremiumModal('Upgrade to premium to access this content');
            return;
        }

        // User has premium access, allow the action
        return true;
    }

    async authenticateUser(email, password) {
        // Simulate API call
        await this.delay(1000);
        
        // Demo users for testing
        const demoUsers = [
            {
                id: 1,
                email: '<EMAIL>',
                password: 'demo123',
                firstName: 'Demo',
                lastName: 'User',
                isPremium: true,
                joinDate: new Date().toISOString()
            },
            {
                id: 2,
                email: '<EMAIL>',
                password: 'password',
                firstName: 'John',
                lastName: 'Doe',
                isPremium: false,
                joinDate: new Date().toISOString()
            }
        ];

        const user = demoUsers.find(u => u.email === email && u.password === password);
        return user || null;
    }

    async checkUserExists(email) {
        // Simulate API call
        await this.delay(500);
        
        // In a real app, this would check the database
        const existingEmails = ['<EMAIL>', '<EMAIL>'];
        return existingEmails.includes(email);
    }

    async createUser(userData) {
        // Simulate API call
        await this.delay(1000);
        
        const newUser = {
            id: Date.now(),
            email: userData.email,
            firstName: userData.firstName,
            lastName: userData.lastName,
            isPremium: false,
            joinDate: new Date().toISOString(),
            progress: {
                completedTutorials: [],
                currentPath: null,
                totalWatchTime: 0
            }
        };

        return newUser;
    }

    setCurrentUser(user) {
        this.currentUser = user;
        localStorage.setItem('dotnetWithMahesh_user', JSON.stringify(user));
        sessionStorage.setItem('dotnetWithMahesh_session', Date.now().toString());
        this.updateUI();
    }

    loadUserFromStorage() {
        const userData = localStorage.getItem('dotnetWithMahesh_user');
        const sessionData = sessionStorage.getItem('dotnetWithMahesh_session');
        
        if (userData && sessionData) {
            // Check if session is still valid (24 hours)
            const sessionTime = parseInt(sessionData);
            const now = Date.now();
            const sessionDuration = 24 * 60 * 60 * 1000; // 24 hours
            
            if (now - sessionTime < sessionDuration) {
                this.currentUser = JSON.parse(userData);
            } else {
                // Session expired
                localStorage.removeItem('dotnetWithMahesh_user');
                sessionStorage.removeItem('dotnetWithMahesh_session');
            }
        }
    }

    updateUI() {
        const authButtons = document.querySelector('.auth-buttons');
        const userMenu = document.querySelector('.user-menu');
        
        if (this.currentUser) {
            // User is logged in
            if (authButtons) {
                authButtons.innerHTML = `
                    <div class="user-dropdown">
                        <button class="user-btn" id="userMenuBtn">
                            <img src="assets/default-avatar.svg" alt="User Avatar" class="user-avatar">
                            <span class="user-name">${this.currentUser.firstName}</span>
                            <i class="fas fa-chevron-down"></i>
                        </button>
                        <div class="user-dropdown-menu" id="userDropdownMenu">
                            <a href="dashboard.html" class="dropdown-item">
                                <i class="fas fa-tachometer-alt"></i>
                                Dashboard
                            </a>
                            <a href="profile.html" class="dropdown-item">
                                <i class="fas fa-user"></i>
                                Profile
                            </a>
                            <a href="progress.html" class="dropdown-item">
                                <i class="fas fa-chart-line"></i>
                                Progress
                            </a>
                            ${!this.currentUser.isPremium ? `
                                <a href="premium.html" class="dropdown-item premium-link">
                                    <i class="fas fa-crown"></i>
                                    Upgrade to Premium
                                </a>
                            ` : ''}
                            <div class="dropdown-divider"></div>
                            <button class="dropdown-item logout-btn">
                                <i class="fas fa-sign-out-alt"></i>
                                Logout
                            </button>
                        </div>
                    </div>
                `;
                
                this.setupUserDropdown();
            }
            
            // Update premium content visibility
            this.updatePremiumContent();
        } else {
            // User is not logged in
            if (authButtons) {
                authButtons.innerHTML = `
                    <a href="login.html" class="btn btn-outline">Login</a>
                    <a href="register.html" class="btn btn-primary">Get Started</a>
                `;
            }
        }
    }

    setupUserDropdown() {
        const userBtn = document.getElementById('userMenuBtn');
        const dropdownMenu = document.getElementById('userDropdownMenu');
        
        if (userBtn && dropdownMenu) {
            userBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                dropdownMenu.classList.toggle('active');
            });
            
            // Close dropdown when clicking outside
            document.addEventListener('click', () => {
                dropdownMenu.classList.remove('active');
            });
        }
    }

    updatePremiumContent() {
        const premiumElements = document.querySelectorAll('.premium-content');
        
        premiumElements.forEach(element => {
            if (this.currentUser && this.currentUser.isPremium) {
                element.classList.remove('locked');
            } else {
                element.classList.add('locked');
            }
        });
    }

    showPremiumModal(message) {
        const modal = document.createElement('div');
        modal.className = 'premium-modal-overlay';
        modal.innerHTML = `
            <div class="premium-modal">
                <div class="premium-modal-header">
                    <h3>Premium Content</h3>
                    <button class="modal-close">&times;</button>
                </div>
                <div class="premium-modal-body">
                    <div class="premium-icon">
                        <i class="fas fa-crown"></i>
                    </div>
                    <p>${message}</p>
                    <div class="premium-actions">
                        ${!this.currentUser ? `
                            <a href="login.html" class="btn btn-primary">Login</a>
                            <a href="register.html" class="btn btn-outline">Sign Up</a>
                        ` : `
                            <a href="premium.html" class="btn btn-primary">Upgrade to Premium</a>
                            <button class="btn btn-outline modal-close">Maybe Later</button>
                        `}
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        // Close modal events
        modal.querySelectorAll('.modal-close').forEach(btn => {
            btn.addEventListener('click', () => {
                modal.remove();
            });
        });
        
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.remove();
            }
        });
    }

    validateEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    showError(message) {
        this.showNotification(message, 'error');
    }

    showSuccess(message) {
        this.showNotification(message, 'success');
    }

    showNotification(message, type) {
        // Use the main app's notification system if available
        if (window.app && window.app.showNotification) {
            window.app.showNotification(message, type);
        } else {
            // Fallback notification
            alert(message);
        }
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // Public methods for external use
    isLoggedIn() {
        return !!this.currentUser;
    }

    isPremiumUser() {
        return this.currentUser && this.currentUser.isPremium;
    }

    getCurrentUser() {
        return this.currentUser;
    }
}

// Initialize authentication system
const authSystem = new AuthSystem();

// Make it globally available
window.authSystem = authSystem;
