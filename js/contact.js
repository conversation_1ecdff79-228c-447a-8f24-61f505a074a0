// Contact form functionality for .NET with Mahesh website

class ContactSystem {
    constructor() {
        this.init();
    }

    init() {
        this.setupContactForm();
        this.setupFormValidation();
        this.setupFAQInteractions();
    }

    setupContactForm() {
        const contactForm = document.getElementById('contactForm');
        if (contactForm) {
            contactForm.addEventListener('submit', this.handleFormSubmit.bind(this));
            
            // Setup real-time validation
            const inputs = contactForm.querySelectorAll('input, select, textarea');
            inputs.forEach(input => {
                input.addEventListener('blur', () => this.validateField(input));
                input.addEventListener('input', () => this.clearFieldError(input));
            });
        }
    }

    async handleFormSubmit(e) {
        e.preventDefault();
        
        const form = e.target;
        const formData = new FormData(form);
        const submitBtn = form.querySelector('button[type="submit"]');
        
        // Validate all fields
        if (!this.validateForm(form)) {
            this.showNotification('Please fix the errors in the form', 'error');
            return;
        }

        // Show loading state
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Sending...';
        submitBtn.disabled = true;

        try {
            // Prepare form data
            const contactData = {
                firstName: formData.get('firstName'),
                lastName: formData.get('lastName'),
                email: formData.get('email'),
                company: formData.get('company'),
                subject: formData.get('subject'),
                message: formData.get('message'),
                newsletter: formData.get('newsletter') === 'on',
                timestamp: new Date().toISOString(),
                userAgent: navigator.userAgent,
                referrer: document.referrer
            };

            // Send contact form (simulate API call)
            const response = await this.submitContactForm(contactData);
            
            if (response.success) {
                this.showSuccessMessage();
                form.reset();
                
                // Subscribe to newsletter if requested
                if (contactData.newsletter) {
                    await this.subscribeToNewsletter(contactData.email);
                }
                
                // Track form submission
                this.trackFormSubmission(contactData.subject);
            } else {
                throw new Error(response.message || 'Failed to send message');
            }
        } catch (error) {
            console.error('Contact form error:', error);
            this.showNotification('Failed to send message. Please try again or contact me directly.', 'error');
        } finally {
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }
    }

    async submitContactForm(contactData) {
        // Simulate API call
        await this.delay(2000);
        
        // In a real application, this would send data to your backend
        console.log('Contact form submitted:', contactData);
        
        // Simulate success response
        return {
            success: true,
            message: 'Message sent successfully',
            id: Date.now().toString()
        };
    }

    async subscribeToNewsletter(email) {
        try {
            // Simulate newsletter subscription
            await this.delay(500);
            console.log('Newsletter subscription:', email);
            return { success: true };
        } catch (error) {
            console.error('Newsletter subscription error:', error);
            return { success: false };
        }
    }

    validateForm(form) {
        const inputs = form.querySelectorAll('input[required], select[required], textarea[required]');
        let isValid = true;

        inputs.forEach(input => {
            if (!this.validateField(input)) {
                isValid = false;
            }
        });

        // Additional email validation
        const emailInput = form.querySelector('input[type="email"]');
        if (emailInput && !this.isValidEmail(emailInput.value)) {
            this.showFieldError(emailInput, 'Please enter a valid email address');
            isValid = false;
        }

        return isValid;
    }

    validateField(field) {
        const value = field.value.trim();
        const fieldName = field.name;
        let isValid = true;
        let errorMessage = '';

        // Clear previous errors
        this.clearFieldError(field);

        // Required field validation
        if (field.hasAttribute('required') && !value) {
            errorMessage = `${this.getFieldLabel(field)} is required`;
            isValid = false;
        }

        // Specific field validations
        switch (fieldName) {
            case 'firstName':
            case 'lastName':
                if (value && value.length < 2) {
                    errorMessage = 'Name must be at least 2 characters long';
                    isValid = false;
                }
                break;

            case 'email':
                if (value && !this.isValidEmail(value)) {
                    errorMessage = 'Please enter a valid email address';
                    isValid = false;
                }
                break;

            case 'message':
                if (value && value.length < 10) {
                    errorMessage = 'Message must be at least 10 characters long';
                    isValid = false;
                }
                break;
        }

        if (!isValid) {
            this.showFieldError(field, errorMessage);
        }

        return isValid;
    }

    showFieldError(field, message) {
        const formGroup = field.closest('.form-group');
        if (formGroup) {
            // Remove existing error
            const existingError = formGroup.querySelector('.field-error');
            if (existingError) {
                existingError.remove();
            }

            // Add error class
            field.classList.add('error');

            // Create error message
            const errorElement = document.createElement('div');
            errorElement.className = 'field-error';
            errorElement.textContent = message;
            formGroup.appendChild(errorElement);
        }
    }

    clearFieldError(field) {
        const formGroup = field.closest('.form-group');
        if (formGroup) {
            field.classList.remove('error');
            const errorElement = formGroup.querySelector('.field-error');
            if (errorElement) {
                errorElement.remove();
            }
        }
    }

    getFieldLabel(field) {
        const label = field.closest('.form-group')?.querySelector('label');
        return label ? label.textContent.replace('*', '').trim() : field.name;
    }

    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    showSuccessMessage() {
        // Create success modal
        const modal = document.createElement('div');
        modal.className = 'success-modal-overlay';
        modal.innerHTML = `
            <div class="success-modal">
                <div class="success-modal-content">
                    <div class="success-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <h3 class="success-title">Message Sent Successfully!</h3>
                    <p class="success-message">
                        Thank you for reaching out! I've received your message and will get back to you 
                        within 24-48 hours. If you have an urgent matter, please mention it in your message.
                    </p>
                    <div class="success-actions">
                        <button class="btn btn-primary modal-close">Got it!</button>
                        <a href="tutorials.html" class="btn btn-outline">Browse Tutorials</a>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // Close modal events
        modal.querySelector('.modal-close').addEventListener('click', () => {
            modal.remove();
        });

        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.remove();
            }
        });

        // Auto-remove after 10 seconds
        setTimeout(() => {
            if (modal.parentNode) {
                modal.remove();
            }
        }, 10000);
    }

    setupFAQInteractions() {
        const faqItems = document.querySelectorAll('.faq-item');
        
        faqItems.forEach(item => {
            const question = item.querySelector('.faq-question');
            const answer = item.querySelector('.faq-answer');
            
            if (question && answer) {
                // Initially hide answers
                answer.style.display = 'none';
                
                // Add click handler
                question.addEventListener('click', () => {
                    const isOpen = answer.style.display === 'block';
                    
                    // Close all other FAQ items
                    faqItems.forEach(otherItem => {
                        const otherAnswer = otherItem.querySelector('.faq-answer');
                        const otherQuestion = otherItem.querySelector('.faq-question');
                        if (otherAnswer && otherQuestion) {
                            otherAnswer.style.display = 'none';
                            otherQuestion.classList.remove('active');
                        }
                    });
                    
                    // Toggle current item
                    if (!isOpen) {
                        answer.style.display = 'block';
                        question.classList.add('active');
                        
                        // Smooth scroll to question
                        question.scrollIntoView({
                            behavior: 'smooth',
                            block: 'nearest'
                        });
                    }
                });
                
                // Add cursor pointer style
                question.style.cursor = 'pointer';
                question.style.userSelect = 'none';
            }
        });
    }

    trackFormSubmission(subject) {
        // Track form submission for analytics
        if (typeof gtag !== 'undefined') {
            gtag('event', 'contact_form_submit', {
                'event_category': 'Contact',
                'event_label': subject,
                'value': 1
            });
        }

        // Store in local analytics
        const submissions = this.getLocalAnalytics();
        submissions.push({
            type: 'contact_form',
            subject: subject,
            timestamp: new Date().toISOString()
        });
        
        localStorage.setItem('dotnetWithMahesh_analytics', JSON.stringify(submissions));
    }

    getLocalAnalytics() {
        try {
            const data = localStorage.getItem('dotnetWithMahesh_analytics');
            return data ? JSON.parse(data) : [];
        } catch (error) {
            return [];
        }
    }

    showNotification(message, type = 'info') {
        // Use the main app's notification system if available
        if (window.app && window.app.showNotification) {
            window.app.showNotification(message, type);
        } else {
            // Fallback notification
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.innerHTML = `
                <div class="notification-content">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
                    <span>${message}</span>
                </div>
                <button class="notification-close">
                    <i class="fas fa-times"></i>
                </button>
            `;

            // Add styles if not already present
            if (!document.querySelector('.notification-styles')) {
                const styles = document.createElement('style');
                styles.className = 'notification-styles';
                styles.textContent = `
                    .notification {
                        position: fixed;
                        top: 20px;
                        right: 20px;
                        background: white;
                        border-radius: 8px;
                        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                        padding: 16px;
                        max-width: 400px;
                        z-index: 10000;
                        display: flex;
                        align-items: center;
                        gap: 12px;
                        animation: slideIn 0.3s ease-out;
                    }
                    .notification-success { border-left: 4px solid #10B981; }
                    .notification-error { border-left: 4px solid #EF4444; }
                    .notification-info { border-left: 4px solid #3B82F6; }
                    .notification-content { flex: 1; display: flex; align-items: center; gap: 8px; }
                    .notification-close { background: none; border: none; cursor: pointer; }
                    @keyframes slideIn {
                        from { transform: translateX(100%); opacity: 0; }
                        to { transform: translateX(0); opacity: 1; }
                    }
                `;
                document.head.appendChild(styles);
            }

            document.body.appendChild(notification);

            // Auto remove after 5 seconds
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.style.animation = 'slideIn 0.3s ease-out reverse';
                    setTimeout(() => notification.remove(), 300);
                }
            }, 5000);

            // Manual close
            notification.querySelector('.notification-close').addEventListener('click', () => {
                notification.style.animation = 'slideIn 0.3s ease-out reverse';
                setTimeout(() => notification.remove(), 300);
            });
        }
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// Initialize contact system when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    const contactSystem = new ContactSystem();
    window.contactSystem = contactSystem;
});
