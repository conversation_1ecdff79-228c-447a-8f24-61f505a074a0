<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Get in touch with <PERSON><PERSON><PERSON> Gadhave for .NET MAUI development questions, collaboration opportunities, and professional inquiries.">
    <meta name="keywords" content="contact, Mahesh Gadhave, .NET MAUI developer, consultation, collaboration">
    <meta name="author" content="Mahesh Gadhave">
    
    <title>Contact - .NET with <PERSON><PERSON><PERSON></title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="assets/favicon.ico">
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/components.css">
    <link rel="stylesheet" href="styles/responsive.css">
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <!-- Navigation Header -->
    <header class="header">
        <nav class="navbar">
            <div class="nav-container">
                <div class="nav-brand">
                    <a href="index.html" class="brand-link">
                        <img src="assets/logo.svg" alt=".NET with Mahesh Logo" class="brand-logo">
                        <span class="brand-text">.NET with Mahesh</span>
                    </a>
                </div>
                
                <div class="nav-menu" id="navMenu">
                    <ul class="nav-list">
                        <li class="nav-item">
                            <a href="index.html" class="nav-link">Home</a>
                        </li>
                        <li class="nav-item">
                            <a href="tutorials.html" class="nav-link">Tutorials</a>
                        </li>
                        <li class="nav-item">
                            <a href="blog.html" class="nav-link">Blog</a>
                        </li>
                        <li class="nav-item">
                            <a href="resources.html" class="nav-link">Resources</a>
                        </li>
                        <li class="nav-item">
                            <a href="contact.html" class="nav-link active">Contact</a>
                        </li>
                    </ul>
                </div>
                
                <div class="nav-actions">
                    <button class="search-btn" id="searchBtn" aria-label="Search">
                        <i class="fas fa-search"></i>
                    </button>
                    <button class="theme-toggle" id="themeToggle" aria-label="Toggle theme">
                        <i class="fas fa-moon"></i>
                    </button>
                    <div class="auth-buttons">
                        <a href="login.html" class="btn btn-outline">Login</a>
                        <a href="register.html" class="btn btn-primary">Get Started</a>
                    </div>
                </div>
                
                <button class="mobile-menu-toggle" id="mobileMenuToggle" aria-label="Toggle mobile menu">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>
            </div>
        </nav>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Page Header -->
        <section class="page-header">
            <div class="container">
                <div class="page-header-content">
                    <h1 class="page-title">Get in Touch</h1>
                    <p class="page-description">
                        Have questions about .NET MAUI? Looking for collaboration opportunities? 
                        I'd love to hear from you and help with your development journey.
                    </p>
                </div>
            </div>
        </section>

        <!-- Contact Section -->
        <section class="contact-section">
            <div class="container">
                <div class="contact-grid">
                    <!-- Contact Form -->
                    <div class="contact-form-container">
                        <div class="form-header">
                            <h2 class="form-title">Send a Message</h2>
                            <p class="form-description">
                                Fill out the form below and I'll get back to you as soon as possible.
                            </p>
                        </div>
                        
                        <form class="contact-form" id="contactForm">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="firstName" class="form-label">First Name *</label>
                                    <input type="text" id="firstName" name="firstName" class="form-input" required>
                                </div>
                                <div class="form-group">
                                    <label for="lastName" class="form-label">Last Name *</label>
                                    <input type="text" id="lastName" name="lastName" class="form-input" required>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label for="email" class="form-label">Email Address *</label>
                                <input type="email" id="email" name="email" class="form-input" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="company" class="form-label">Company/Organization</label>
                                <input type="text" id="company" name="company" class="form-input">
                            </div>
                            
                            <div class="form-group">
                                <label for="subject" class="form-label">Subject *</label>
                                <select id="subject" name="subject" class="form-select" required>
                                    <option value="">Select a subject</option>
                                    <option value="general">General Inquiry</option>
                                    <option value="tutorial">Tutorial Question</option>
                                    <option value="collaboration">Collaboration</option>
                                    <option value="consulting">Consulting Services</option>
                                    <option value="speaking">Speaking Engagement</option>
                                    <option value="feedback">Feedback</option>
                                    <option value="other">Other</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label for="message" class="form-label">Message *</label>
                                <textarea id="message" name="message" class="form-textarea" rows="6" required 
                                          placeholder="Tell me about your project, question, or how I can help you..."></textarea>
                            </div>
                            
                            <div class="form-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" name="newsletter" class="form-checkbox">
                                    <span class="checkbox-text">Subscribe to newsletter for .NET MAUI updates</span>
                                </label>
                            </div>
                            
                            <button type="submit" class="btn btn-primary btn-large">
                                <i class="fas fa-paper-plane"></i>
                                Send Message
                            </button>
                        </form>
                    </div>
                    
                    <!-- Contact Information -->
                    <div class="contact-info">
                        <div class="contact-card">
                            <div class="contact-card-header">
                                <h3 class="contact-card-title">Contact Information</h3>
                                <p class="contact-card-description">
                                    Reach out through any of these channels
                                </p>
                            </div>
                            
                            <div class="contact-methods">
                                <div class="contact-method">
                                    <div class="contact-icon">
                                        <i class="fas fa-envelope"></i>
                                    </div>
                                    <div class="contact-details">
                                        <h4 class="contact-method-title">Email</h4>
                                        <p class="contact-method-text"><EMAIL></p>
                                        <a href="mailto:<EMAIL>" class="contact-link">Send Email</a>
                                    </div>
                                </div>
                                
                                <div class="contact-method">
                                    <div class="contact-icon">
                                        <i class="fab fa-linkedin"></i>
                                    </div>
                                    <div class="contact-details">
                                        <h4 class="contact-method-title">LinkedIn</h4>
                                        <p class="contact-method-text">Professional networking and updates</p>
                                        <a href="https://www.linkedin.com/in/mahesh-gadhave-xamarin-maui-developer/" 
                                           target="_blank" rel="noopener" class="contact-link">Connect on LinkedIn</a>
                                    </div>
                                </div>
                                
                                <div class="contact-method">
                                    <div class="contact-icon">
                                        <i class="fab fa-github"></i>
                                    </div>
                                    <div class="contact-details">
                                        <h4 class="contact-method-title">GitHub</h4>
                                        <p class="contact-method-text">Code samples and open source projects</p>
                                        <a href="https://github.com/maheshgadhave" target="_blank" rel="noopener" class="contact-link">View GitHub</a>
                                    </div>
                                </div>
                                
                                <div class="contact-method">
                                    <div class="contact-icon">
                                        <i class="fab fa-twitter"></i>
                                    </div>
                                    <div class="contact-details">
                                        <h4 class="contact-method-title">Twitter</h4>
                                        <p class="contact-method-text">Latest updates and tech discussions</p>
                                        <a href="https://twitter.com/maheshgadhave" target="_blank" rel="noopener" class="contact-link">Follow on Twitter</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Response Time -->
                        <div class="response-info">
                            <div class="response-card">
                                <div class="response-icon">
                                    <i class="fas fa-clock"></i>
                                </div>
                                <div class="response-content">
                                    <h4 class="response-title">Response Time</h4>
                                    <p class="response-text">
                                        I typically respond to messages within 24-48 hours during business days.
                                        For urgent matters, please mention it in your message.
                                    </p>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Availability -->
                        <div class="availability-info">
                            <div class="availability-card">
                                <div class="availability-icon">
                                    <i class="fas fa-calendar-alt"></i>
                                </div>
                                <div class="availability-content">
                                    <h4 class="availability-title">Availability</h4>
                                    <p class="availability-text">
                                        Available for consulting, speaking engagements, and collaboration opportunities.
                                        Let's discuss how we can work together!
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- FAQ Section -->
        <section class="faq-section">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">Frequently Asked Questions</h2>
                    <p class="section-description">
                        Common questions about .NET MAUI development and my services
                    </p>
                </div>
                
                <div class="faq-grid">
                    <div class="faq-item">
                        <h3 class="faq-question">Do you offer one-on-one consulting?</h3>
                        <p class="faq-answer">
                            Yes, I offer personalized consulting sessions for .NET MAUI development, 
                            architecture reviews, and project guidance. Contact me to discuss your needs.
                        </p>
                    </div>
                    
                    <div class="faq-item">
                        <h3 class="faq-question">Can you help with existing Xamarin.Forms migration?</h3>
                        <p class="faq-answer">
                            Absolutely! I have extensive experience helping teams migrate from Xamarin.Forms 
                            to .NET MAUI. I can provide migration strategies and hands-on assistance.
                        </p>
                    </div>
                    
                    <div class="faq-item">
                        <h3 class="faq-question">Do you create custom tutorials for teams?</h3>
                        <p class="faq-answer">
                            Yes, I can create custom training materials and tutorials tailored to your 
                            team's specific needs and use cases. Let's discuss your requirements.
                        </p>
                    </div>
                    
                    <div class="faq-item">
                        <h3 class="faq-question">Are you available for speaking at events?</h3>
                        <p class="faq-answer">
                            I'm always interested in speaking opportunities at conferences, meetups, 
                            and corporate events about .NET MAUI and mobile development.
                        </p>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <div class="footer-brand">
                        <img src="assets/logo.svg" alt=".NET with Mahesh Logo" class="footer-logo">
                        <h3 class="footer-brand-text">.NET with Mahesh</h3>
                        <p class="footer-brand-description">
                            Master .NET MAUI development with comprehensive tutorials and hands-on projects.
                        </p>
                    </div>
                </div>
                
                <div class="footer-section">
                    <h4 class="footer-title">Learning</h4>
                    <ul class="footer-links">
                        <li><a href="tutorials.html">All Tutorials</a></li>
                        <li><a href="learning-path-beginner.html">Beginner Path</a></li>
                        <li><a href="learning-path-intermediate.html">Intermediate Path</a></li>
                        <li><a href="learning-path-advanced.html">Advanced Path</a></li>
                        <li><a href="resources.html">Resources</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4 class="footer-title">Content</h4>
                    <ul class="footer-links">
                        <li><a href="blog.html">Blog Posts</a></li>
                        <li><a href="code-samples.html">Code Samples</a></li>
                        <li><a href="presentations.html">Presentations</a></li>
                        <li><a href="projects.html">Sample Projects</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4 class="footer-title">Connect</h4>
                    <ul class="footer-links">
                        <li><a href="contact.html">Contact</a></li>
                        <li><a href="https://www.linkedin.com/in/mahesh-gadhave-xamarin-maui-developer/" target="_blank" rel="noopener">LinkedIn</a></li>
                        <li><a href="https://github.com/maheshgadhave" target="_blank" rel="noopener">GitHub</a></li>
                        <li><a href="mailto:<EMAIL>">Email</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4 class="footer-title">Legal</h4>
                    <ul class="footer-links">
                        <li><a href="privacy-policy.html">Privacy Policy</a></li>
                        <li><a href="terms-of-service.html">Terms of Service</a></li>
                        <li><a href="cookie-policy.html">Cookie Policy</a></li>
                    </ul>
                </div>
            </div>
            
            <div class="footer-bottom">
                <div class="footer-copyright">
                    <p>&copy; 2024 .NET with Mahesh. All rights reserved.</p>
                </div>
                <div class="footer-social">
                    <a href="https://www.linkedin.com/in/mahesh-gadhave-xamarin-maui-developer/" target="_blank" rel="noopener" class="social-link">
                        <i class="fab fa-linkedin"></i>
                    </a>
                    <a href="https://github.com/maheshgadhave" target="_blank" rel="noopener" class="social-link">
                        <i class="fab fa-github"></i>
                    </a>
                    <a href="https://twitter.com/maheshgadhave" target="_blank" rel="noopener" class="social-link">
                        <i class="fab fa-twitter"></i>
                    </a>
                    <a href="mailto:<EMAIL>" class="social-link">
                        <i class="fas fa-envelope"></i>
                    </a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="js/utils.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/search.js"></script>
    <script src="js/contact.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
