/* Header and Navigation */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background-color: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--gray-200);
    z-index: var(--z-fixed);
    transition: all var(--transition-fast);
}

.navbar {
    padding: var(--space-4) 0;
}

.nav-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--space-4);
}

.nav-brand {
    display: flex;
    align-items: center;
}

.brand-link {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    text-decoration: none;
    color: var(--gray-900);
}

.brand-logo {
    width: 32px;
    height: 32px;
}

.brand-text {
    font-size: var(--text-xl);
    font-weight: 700;
    color: var(--primary-color);
}

.nav-menu {
    display: none;
}

.nav-list {
    display: flex;
    list-style: none;
    gap: var(--space-8);
}

.nav-link {
    font-weight: 500;
    color: var(--gray-600);
    transition: color var(--transition-fast);
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-color);
}

.nav-link.active::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    right: 0;
    height: 2px;
    background-color: var(--primary-color);
    border-radius: var(--radius-full);
}

.nav-actions {
    display: flex;
    align-items: center;
    gap: var(--space-4);
}

.search-btn,
.theme-toggle {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border: none;
    background-color: transparent;
    color: var(--gray-600);
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.search-btn:hover,
.theme-toggle:hover {
    background-color: var(--gray-100);
    color: var(--primary-color);
}

.auth-buttons {
    display: none;
    gap: var(--space-3);
}

.mobile-menu-toggle {
    display: flex;
    flex-direction: column;
    justify-content: center;
    width: 24px;
    height: 24px;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0;
}

.mobile-menu-toggle span {
    display: block;
    height: 2px;
    width: 100%;
    background-color: var(--gray-600);
    margin-bottom: 4px;
    transition: all var(--transition-fast);
}

.mobile-menu-toggle span:last-child {
    margin-bottom: 0;
}

/* Search Overlay */
.search-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
    z-index: var(--z-modal);
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
}

.search-overlay.active {
    opacity: 1;
    visibility: visible;
}

.search-container {
    position: relative;
    max-width: 600px;
    margin: var(--space-20) auto var(--space-8);
    padding: 0 var(--space-4);
}

.search-box {
    display: flex;
    background-color: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    overflow: hidden;
}

.search-input {
    flex: 1;
    padding: var(--space-4) var(--space-6);
    border: none;
    font-size: var(--text-lg);
    outline: none;
}

.search-submit {
    padding: var(--space-4) var(--space-6);
    background-color: var(--primary-color);
    color: var(--white);
    border: none;
    cursor: pointer;
    transition: background-color var(--transition-fast);
}

.search-submit:hover {
    background-color: var(--primary-dark);
}

.search-close {
    position: absolute;
    top: -40px;
    right: 0;
    width: 32px;
    height: 32px;
    background-color: var(--white);
    border: none;
    border-radius: var(--radius-full);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--gray-600);
}

.search-results {
    background-color: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    margin-top: var(--space-4);
    max-height: 400px;
    overflow-y: auto;
    display: none;
}

/* Hero Section */
.hero {
    padding: calc(80px + var(--space-20)) 0 var(--space-20);
    background: linear-gradient(135deg, var(--gray-50) 0%, var(--white) 100%);
    position: relative;
    overflow: hidden;
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--space-4);
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--space-12);
    align-items: center;
}

.hero-title {
    font-size: var(--text-4xl);
    font-weight: 700;
    line-height: 1.1;
    margin-bottom: var(--space-6);
    color: var(--gray-900);
}

.hero-description {
    font-size: var(--text-xl);
    color: var(--gray-600);
    margin-bottom: var(--space-8);
    line-height: 1.6;
}

.hero-stats {
    display: flex;
    gap: var(--space-8);
    margin-bottom: var(--space-8);
}

.stat {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: var(--text-2xl);
    font-weight: 700;
    color: var(--primary-color);
}

.stat-label {
    font-size: var(--text-sm);
    color: var(--gray-600);
}

.hero-actions {
    display: flex;
    gap: var(--space-4);
    flex-wrap: wrap;
}

.hero-visual {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
}

.hero-image {
    max-width: 100%;
    height: auto;
}

.hero-img {
    width: 100%;
    height: auto;
    max-width: 500px;
}

.floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
}

.floating-card {
    position: absolute;
    background-color: var(--white);
    padding: var(--space-3) var(--space-4);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    display: flex;
    align-items: center;
    gap: var(--space-2);
    font-size: var(--text-sm);
    font-weight: 500;
    color: var(--gray-700);
}

.floating-card i {
    font-size: var(--text-lg);
    color: var(--primary-color);
}

.card-1 {
    top: 20%;
    left: 10%;
    animation: float 3s ease-in-out infinite;
}

.card-2 {
    top: 15%;
    right: 15%;
    animation: float 3s ease-in-out infinite 0.5s;
}

.card-3 {
    bottom: 30%;
    left: 5%;
    animation: float 3s ease-in-out infinite 1s;
}

.card-4 {
    bottom: 20%;
    right: 10%;
    animation: float 3s ease-in-out infinite 1.5s;
}

/* Section Styles */
.section-header {
    text-align: center;
    margin-bottom: var(--space-12);
}

.section-title {
    font-size: var(--text-3xl);
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--space-4);
}

.section-description {
    font-size: var(--text-lg);
    color: var(--gray-600);
    max-width: 600px;
    margin: 0 auto;
}

.section-footer {
    text-align: center;
    margin-top: var(--space-12);
}

/* Featured Tutorials */
.featured-tutorials {
    padding: var(--space-20) 0;
    background-color: var(--white);
}

.tutorial-filters {
    display: flex;
    justify-content: center;
    gap: var(--space-4);
    margin-bottom: var(--space-12);
    flex-wrap: wrap;
}

.filter-btn {
    padding: var(--space-2) var(--space-4);
    border: 1px solid var(--gray-300);
    background-color: var(--white);
    color: var(--gray-600);
    border-radius: var(--radius-full);
    cursor: pointer;
    transition: all var(--transition-fast);
    font-size: var(--text-sm);
    font-weight: 500;
}

.filter-btn:hover,
.filter-btn.active {
    background-color: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
}

.tutorial-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--space-8);
}

/* Responsive Design */
@media (min-width: 640px) {
    .nav-menu {
        display: block;
    }
    
    .auth-buttons {
        display: flex;
    }
    
    .mobile-menu-toggle {
        display: none;
    }
    
    .hero-content {
        grid-template-columns: 1fr 1fr;
    }
    
    .hero-title {
        font-size: var(--text-5xl);
    }
    
    .hero-actions {
        flex-wrap: nowrap;
    }
}

/* Learning Paths */
.learning-paths {
    padding: var(--space-20) 0;
    background-color: var(--gray-50);
}

.paths-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--space-8);
}

.path-card {
    background-color: var(--white);
    padding: var(--space-8);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    text-align: center;
    position: relative;
    transition: all var(--transition-normal);
}

.path-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

.path-card.featured {
    border: 2px solid var(--primary-color);
    transform: scale(1.05);
}

.path-badge {
    position: absolute;
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
    background-color: var(--primary-color);
    color: var(--white);
    padding: var(--space-1) var(--space-3);
    border-radius: var(--radius-full);
    font-size: var(--text-xs);
    font-weight: 600;
}

.path-icon {
    width: 60px;
    height: 60px;
    background-color: var(--primary-color);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--space-6);
    color: var(--white);
    font-size: var(--text-2xl);
}

.path-title {
    font-size: var(--text-xl);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--space-4);
}

.path-description {
    color: var(--gray-600);
    margin-bottom: var(--space-6);
    line-height: 1.6;
}

.path-meta {
    display: flex;
    justify-content: center;
    gap: var(--space-6);
    margin-bottom: var(--space-6);
}

.path-duration,
.path-lessons {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    font-size: var(--text-sm);
    color: var(--gray-500);
}

.path-link {
    display: inline-block;
    background-color: var(--primary-color);
    color: var(--white);
    padding: var(--space-3) var(--space-6);
    border-radius: var(--radius-lg);
    text-decoration: none;
    font-weight: 500;
    transition: background-color var(--transition-fast);
}

.path-link:hover {
    background-color: var(--primary-dark);
    color: var(--white);
}

/* Newsletter */
.newsletter {
    padding: var(--space-20) 0;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: var(--white);
}

.newsletter-content {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--space-8);
    align-items: center;
    text-align: center;
}

.newsletter-title {
    font-size: var(--text-3xl);
    font-weight: 700;
    margin-bottom: var(--space-4);
    color: var(--white);
}

.newsletter-description {
    font-size: var(--text-lg);
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 0;
}

.newsletter-form .form-group {
    max-width: 400px;
    margin: 0 auto;
}

.newsletter-form .form-input {
    background-color: var(--white);
    border-color: transparent;
}

.newsletter-form .btn-primary {
    background-color: var(--white);
    color: var(--primary-color);
    border-color: var(--white);
}

.newsletter-form .btn-primary:hover {
    background-color: var(--gray-100);
    color: var(--primary-color);
}

.newsletter-privacy {
    margin-top: var(--space-4);
    font-size: var(--text-sm);
    color: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-2);
}

/* About Preview */
.about-preview {
    padding: var(--space-20) 0;
    background-color: var(--white);
}

.about-content {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--space-12);
    align-items: center;
}

.about-title {
    font-size: var(--text-3xl);
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--space-6);
}

.about-description {
    font-size: var(--text-lg);
    color: var(--gray-600);
    line-height: 1.6;
    margin-bottom: var(--space-8);
}

.about-highlights {
    display: flex;
    gap: var(--space-6);
    margin-bottom: var(--space-8);
    flex-wrap: wrap;
}

.highlight-item {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    font-size: var(--text-sm);
    font-weight: 500;
    color: var(--gray-700);
}

.highlight-item i {
    color: var(--primary-color);
    font-size: var(--text-lg);
}

.about-actions {
    display: flex;
    gap: var(--space-4);
    flex-wrap: wrap;
}

.about-image {
    text-align: center;
}

.profile-img {
    width: 200px;
    height: 200px;
    border-radius: var(--radius-full);
    object-fit: cover;
    box-shadow: var(--shadow-xl);
}

@media (min-width: 1024px) {
    .hero-title {
        font-size: var(--text-6xl);
    }

    .tutorial-grid {
        grid-template-columns: repeat(3, 1fr);
    }

    .newsletter-content {
        grid-template-columns: 1fr 1fr;
        text-align: left;
    }

    .about-content {
        grid-template-columns: 2fr 1fr;
    }
}

/* Footer */
.footer {
    background-color: var(--gray-900);
    color: var(--gray-300);
    padding: var(--space-16) 0 var(--space-8);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--space-8);
    margin-bottom: var(--space-12);
}

.footer-section {
    display: flex;
    flex-direction: column;
}

.footer-brand {
    margin-bottom: var(--space-6);
}

.footer-logo {
    width: 32px;
    height: 32px;
    margin-bottom: var(--space-4);
}

.footer-brand-text {
    font-size: var(--text-xl);
    font-weight: 700;
    color: var(--white);
    margin-bottom: var(--space-4);
}

.footer-brand-description {
    color: var(--gray-400);
    line-height: 1.6;
}

.footer-title {
    font-size: var(--text-lg);
    font-weight: 600;
    color: var(--white);
    margin-bottom: var(--space-4);
}

.footer-links {
    list-style: none;
    display: flex;
    flex-direction: column;
    gap: var(--space-3);
}

.footer-links a {
    color: var(--gray-400);
    text-decoration: none;
    transition: color var(--transition-fast);
}

.footer-links a:hover {
    color: var(--white);
}

.footer-bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: var(--space-8);
    border-top: 1px solid var(--gray-700);
    flex-wrap: wrap;
    gap: var(--space-4);
}

.footer-copyright {
    color: var(--gray-400);
    font-size: var(--text-sm);
}

.footer-social {
    display: flex;
    gap: var(--space-4);
}

.social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background-color: var(--gray-800);
    color: var(--gray-400);
    border-radius: var(--radius-lg);
    text-decoration: none;
    transition: all var(--transition-fast);
}

.social-link:hover {
    background-color: var(--primary-color);
    color: var(--white);
    transform: translateY(-2px);
}

/* Tutorial Cards */
.tutorial-card {
    background-color: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    transition: all var(--transition-normal);
    display: flex;
    flex-direction: column;
}

.tutorial-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

.tutorial-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
    background-color: var(--gray-100);
}

.tutorial-content {
    padding: var(--space-6);
    flex: 1;
    display: flex;
    flex-direction: column;
}

.tutorial-meta {
    display: flex;
    align-items: center;
    gap: var(--space-4);
    margin-bottom: var(--space-4);
}

.tutorial-level {
    padding: var(--space-1) var(--space-3);
    border-radius: var(--radius-full);
    font-size: var(--text-xs);
    font-weight: 600;
    text-transform: uppercase;
}

.tutorial-level.beginner {
    background-color: var(--secondary-color);
    color: var(--white);
}

.tutorial-level.intermediate {
    background-color: var(--accent-color);
    color: var(--white);
}

.tutorial-level.advanced {
    background-color: #EF4444;
    color: var(--white);
}

.tutorial-duration {
    display: flex;
    align-items: center;
    gap: var(--space-1);
    font-size: var(--text-sm);
    color: var(--gray-500);
}

.tutorial-title {
    font-size: var(--text-xl);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--space-3);
    line-height: 1.3;
}

.tutorial-description {
    color: var(--gray-600);
    line-height: 1.6;
    margin-bottom: var(--space-6);
    flex: 1;
}

.tutorial-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.tutorial-link {
    color: var(--primary-color);
    font-weight: 500;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: var(--space-2);
    transition: color var(--transition-fast);
}

.tutorial-link:hover {
    color: var(--primary-dark);
}

.tutorial-premium {
    display: flex;
    align-items: center;
    gap: var(--space-1);
    font-size: var(--text-sm);
    color: var(--accent-color);
    font-weight: 500;
}

/* Responsive adjustments */
@media (max-width: 639px) {
    .footer-bottom {
        flex-direction: column;
        text-align: center;
    }

    .tutorial-grid {
        grid-template-columns: 1fr;
    }
}
