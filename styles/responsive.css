/* Mobile First Responsive Design */

/* Extra Small Devices (phones, 320px and up) */
@media (max-width: 479px) {
    .container {
        padding: 0 var(--space-3);
    }
    
    .hero-title {
        font-size: var(--text-3xl);
    }
    
    .hero-description {
        font-size: var(--text-lg);
    }
    
    .hero-stats {
        flex-direction: column;
        gap: var(--space-4);
    }
    
    .hero-actions {
        flex-direction: column;
        width: 100%;
    }
    
    .hero-actions .btn {
        width: 100%;
        justify-content: center;
    }
    
    .section-title {
        font-size: var(--text-2xl);
    }
    
    .tutorial-filters {
        gap: var(--space-2);
    }
    
    .filter-btn {
        padding: var(--space-1) var(--space-3);
        font-size: var(--text-xs);
    }
    
    .paths-grid {
        grid-template-columns: 1fr;
        gap: var(--space-6);
    }
    
    .path-card {
        padding: var(--space-6);
    }
    
    .path-meta {
        flex-direction: column;
        gap: var(--space-3);
    }
    
    .newsletter-form .form-group {
        flex-direction: column;
        gap: var(--space-3);
    }
    
    .newsletter-form .btn {
        width: 100%;
    }
    
    .about-highlights {
        flex-direction: column;
        gap: var(--space-4);
    }
    
    .about-actions {
        flex-direction: column;
        width: 100%;
    }
    
    .about-actions .btn {
        width: 100%;
        justify-content: center;
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        gap: var(--space-6);
        text-align: center;
    }
}

/* Small Devices (landscape phones, 480px and up) */
@media (min-width: 480px) and (max-width: 639px) {
    .hero-stats {
        justify-content: center;
    }
    
    .tutorial-grid {
        grid-template-columns: 1fr;
    }
    
    .paths-grid {
        grid-template-columns: 1fr;
    }
    
    .newsletter-content {
        text-align: center;
    }
    
    .footer-content {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Medium Devices (tablets, 640px and up) */
@media (min-width: 640px) and (max-width: 767px) {
    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .hero-visual {
        order: -1;
    }
    
    .tutorial-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .paths-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .newsletter-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .about-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .about-image {
        order: -1;
    }
}

/* Large Devices (desktops, 768px and up) */
@media (min-width: 768px) and (max-width: 1023px) {
    .hero-title {
        font-size: var(--text-5xl);
    }
    
    .tutorial-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .paths-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .newsletter-content {
        grid-template-columns: 1fr 1fr;
        text-align: left;
    }
    
    .about-content {
        grid-template-columns: 2fr 1fr;
    }
    
    .footer-content {
        grid-template-columns: repeat(3, 1fr);
    }
}

/* Extra Large Devices (large desktops, 1024px and up) */
@media (min-width: 1024px) {
    .hero-title {
        font-size: var(--text-6xl);
    }
    
    .tutorial-grid {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .paths-grid {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .newsletter-content {
        grid-template-columns: 1fr 1fr;
        text-align: left;
    }
    
    .about-content {
        grid-template-columns: 2fr 1fr;
    }
    
    .footer-content {
        grid-template-columns: repeat(5, 1fr);
    }
}

/* Ultra Wide Screens (1400px and up) */
@media (min-width: 1400px) {
    .container {
        max-width: 1400px;
    }
    
    .hero-container {
        max-width: 1400px;
    }
    
    .tutorial-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

/* Mobile Navigation Overrides */
@media (max-width: 639px) {
    .nav-menu {
        position: fixed;
        top: 100%;
        left: 0;
        right: 0;
        background-color: var(--white);
        border-top: 1px solid var(--gray-200);
        box-shadow: var(--shadow-lg);
        transform: translateY(-100%);
        transition: transform var(--transition-normal);
        z-index: var(--z-dropdown);
    }
    
    .nav-menu.active {
        transform: translateY(0);
    }
    
    .nav-list {
        flex-direction: column;
        padding: var(--space-6);
        gap: var(--space-4);
    }
    
    .nav-link {
        padding: var(--space-3) 0;
        border-bottom: 1px solid var(--gray-100);
        width: 100%;
        text-align: center;
    }
    
    .nav-link.active::after {
        display: none;
    }
    
    .nav-actions {
        gap: var(--space-2);
    }
    
    .auth-buttons {
        display: none;
    }
    
    .mobile-menu-toggle.active span:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px);
    }
    
    .mobile-menu-toggle.active span:nth-child(2) {
        opacity: 0;
    }
    
    .mobile-menu-toggle.active span:nth-child(3) {
        transform: rotate(-45deg) translate(7px, -6px);
    }
}

/* Print Styles */
@media print {
    .header,
    .footer,
    .search-overlay,
    .floating-elements,
    .newsletter {
        display: none;
    }
    
    .main-content {
        margin-top: 0;
    }
    
    .hero {
        padding: var(--space-8) 0;
        background: none;
    }
    
    .hero-visual {
        display: none;
    }
    
    .tutorial-card,
    .path-card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid var(--gray-300);
    }
    
    a {
        color: var(--gray-900);
        text-decoration: underline;
    }
    
    .btn {
        border: 1px solid var(--gray-900);
        background: none;
        color: var(--gray-900);
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
    
    .floating-card {
        animation: none;
    }
    
    .animate-float {
        animation: none;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    :root {
        --primary-color: #0000FF;
        --primary-dark: #000080;
        --gray-600: #000000;
        --gray-400: #666666;
    }
    
    .btn-outline {
        border-width: 2px;
    }
    
    .tutorial-card,
    .path-card {
        border: 2px solid var(--gray-900);
    }
}

/* Dark Mode Responsive Adjustments */
@media (prefers-color-scheme: dark) {
    .header {
        background-color: rgba(17, 24, 39, 0.95);
        border-bottom-color: var(--gray-700);
    }
    
    .search-box {
        background-color: var(--gray-800);
    }
    
    .search-input {
        background-color: var(--gray-800);
        color: var(--white);
    }
    
    .search-close {
        background-color: var(--gray-700);
        color: var(--gray-300);
    }
    
    .hero {
        background: linear-gradient(135deg, var(--gray-900) 0%, var(--gray-800) 100%);
    }
    
    .floating-card {
        background-color: var(--gray-800);
        color: var(--gray-200);
    }
}
