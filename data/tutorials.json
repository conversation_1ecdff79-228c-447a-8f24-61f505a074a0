{"tutorials": [{"id": 1, "title": "Getting Started with .NET MAUI", "description": "Learn the basics of .NET MAUI and create your first cross-platform application. This comprehensive tutorial covers project setup, basic UI elements, data binding, and deployment to multiple platforms.", "level": "beginner", "duration": "45 min", "estimatedTime": 45, "image": "assets/tutorials/getting-started.jpg", "videoUrl": "https://example.com/videos/getting-started-maui", "codeUrl": "https://github.com/dotnetwithmahesh/getting-started-maui", "premium": false, "category": "basics", "tags": ["maui", "getting-started", "cross-platform", "mobile", "desktop"], "publishDate": "2024-01-15T10:00:00Z", "lastUpdated": "2024-01-20T14:30:00Z", "views": 1250, "rating": 4.8, "ratingCount": 156, "completions": 890, "difficulty": 1, "prerequisites": [], "learningObjectives": ["Understand .NET MAUI architecture", "Set up development environment", "Create your first MAUI project", "Build basic UI with XAML", "Deploy to Android and iOS"], "chapters": [{"id": 1, "title": "Introduction to .NET MAUI", "duration": "8 min", "videoUrl": "https://example.com/videos/chapter1"}, {"id": 2, "title": "Setting Up Development Environment", "duration": "12 min", "videoUrl": "https://example.com/videos/chapter2"}, {"id": 3, "title": "Creating Your First Project", "duration": "15 min", "videoUrl": "https://example.com/videos/chapter3"}, {"id": 4, "title": "Building the User Interface", "duration": "10 min", "videoUrl": "https://example.com/videos/chapter4"}]}, {"id": 2, "title": "MVVM Pattern in .NET MAUI", "description": "Master the Model-View-ViewModel pattern for clean, maintainable code. Learn data binding, commands, and proper separation of concerns in your MAUI applications.", "level": "intermediate", "duration": "60 min", "estimatedTime": 60, "image": "assets/tutorials/mvvm-pattern.jpg", "videoUrl": "https://example.com/videos/mvvm-pattern-maui", "codeUrl": "https://github.com/dotnetwithmahesh/mvvm-pattern-maui", "premium": true, "category": "architecture", "tags": ["mvvm", "architecture", "data-binding", "commands", "design-patterns"], "publishDate": "2024-01-20T09:00:00Z", "lastUpdated": "2024-01-25T16:45:00Z", "views": 980, "rating": 4.9, "ratingCount": 124, "completions": 650, "difficulty": 3, "prerequisites": ["Getting Started with .NET MAUI"], "learningObjectives": ["Understand MVVM architecture", "Implement ViewModels and Models", "Master data binding techniques", "Create and use Commands", "Handle property change notifications"], "chapters": [{"id": 1, "title": "MVVM Architecture Overview", "duration": "10 min", "videoUrl": "https://example.com/videos/mvvm-chapter1"}, {"id": 2, "title": "Creating ViewModels", "duration": "15 min", "videoUrl": "https://example.com/videos/mvvm-chapter2"}, {"id": 3, "title": "Data Binding Fundamentals", "duration": "20 min", "videoUrl": "https://example.com/videos/mvvm-chapter3"}, {"id": 4, "title": "Commands and Event Handling", "duration": "15 min", "videoUrl": "https://example.com/videos/mvvm-chapter4"}]}, {"id": 3, "title": "Custom Controls and Renderers", "description": "Create custom controls and platform-specific renderers for advanced UI scenarios. Build reusable components and extend existing controls for your applications.", "level": "advanced", "duration": "90 min", "estimatedTime": 90, "image": "assets/tutorials/custom-controls.jpg", "videoUrl": "https://example.com/videos/custom-controls-maui", "codeUrl": "https://github.com/dotnetwithmahesh/custom-controls-maui", "premium": true, "category": "ui", "tags": ["custom-controls", "renderers", "ui", "advanced", "platform-specific"], "publishDate": "2024-01-25T11:00:00Z", "lastUpdated": "2024-02-01T10:20:00Z", "views": 720, "rating": 4.7, "ratingCount": 89, "completions": 420, "difficulty": 4, "prerequisites": ["MVVM Pattern in .NET MAUI", "Data Binding Fundamentals"], "learningObjectives": ["Create custom controls from scratch", "Understand control templating", "Implement platform-specific renderers", "Handle custom events and properties", "Package controls for reuse"], "chapters": [{"id": 1, "title": "Custom Control Basics", "duration": "20 min", "videoUrl": "https://example.com/videos/custom-controls-chapter1"}, {"id": 2, "title": "Control Templates and Styling", "duration": "25 min", "videoUrl": "https://example.com/videos/custom-controls-chapter2"}, {"id": 3, "title": "Platform-Specific Renderers", "duration": "30 min", "videoUrl": "https://example.com/videos/custom-controls-chapter3"}, {"id": 4, "title": "Packaging and Distribution", "duration": "15 min", "videoUrl": "https://example.com/videos/custom-controls-chapter4"}]}, {"id": 4, "title": "Data Binding Fundamentals", "description": "Understand data binding concepts and implement them in your MAUI apps. Learn about binding modes, converters, validation, and best practices for data flow.", "level": "beginner", "duration": "30 min", "estimatedTime": 30, "image": "assets/tutorials/data-binding.jpg", "videoUrl": "https://example.com/videos/data-binding-maui", "codeUrl": "https://github.com/dotnetwithmahesh/data-binding-maui", "premium": false, "category": "basics", "tags": ["data-binding", "fundamentals", "ui", "xaml", "properties"], "publishDate": "2024-02-01T08:30:00Z", "lastUpdated": "2024-02-05T12:15:00Z", "views": 1100, "rating": 4.6, "ratingCount": 142, "completions": 780, "difficulty": 2, "prerequisites": ["Getting Started with .NET MAUI"], "learningObjectives": ["Understand data binding concepts", "Implement one-way and two-way binding", "Use binding converters", "Handle binding errors", "Optimize binding performance"], "chapters": [{"id": 1, "title": "Data Binding Basics", "duration": "8 min", "videoUrl": "https://example.com/videos/data-binding-chapter1"}, {"id": 2, "title": "Binding Modes and Paths", "duration": "10 min", "videoUrl": "https://example.com/videos/data-binding-chapter2"}, {"id": 3, "title": "Value Converters", "duration": "12 min", "videoUrl": "https://example.com/videos/data-binding-chapter3"}]}, {"id": 5, "title": "Navigation and Shell", "description": "Implement complex navigation patterns using .NET MAUI Shell. Learn about routes, navigation parameters, deep linking, and creating intuitive app navigation flows.", "level": "intermediate", "duration": "75 min", "estimatedTime": 75, "image": "assets/tutorials/navigation-shell.jpg", "videoUrl": "https://example.com/videos/navigation-shell-maui", "codeUrl": "https://github.com/dotnetwithmahesh/navigation-shell-maui", "premium": true, "category": "navigation", "tags": ["navigation", "shell", "routing", "deep-linking", "parameters"], "publishDate": "2024-02-05T13:00:00Z", "lastUpdated": "2024-02-10T09:30:00Z", "views": 850, "rating": 4.8, "ratingCount": 98, "completions": 520, "difficulty": 3, "prerequisites": ["MVVM Pattern in .NET MAUI"], "learningObjectives": ["Master Shell navigation", "Implement route-based navigation", "Handle navigation parameters", "Create deep links", "Build complex navigation hierarchies"], "chapters": [{"id": 1, "title": "Shell Navigation Overview", "duration": "15 min", "videoUrl": "https://example.com/videos/navigation-chapter1"}, {"id": 2, "title": "Routes and Navigation", "duration": "20 min", "videoUrl": "https://example.com/videos/navigation-chapter2"}, {"id": 3, "title": "Navigation Parameters", "duration": "20 min", "videoUrl": "https://example.com/videos/navigation-chapter3"}, {"id": 4, "title": "Deep Linking and Advanced Scenarios", "duration": "20 min", "videoUrl": "https://example.com/videos/navigation-chapter4"}]}, {"id": 6, "title": "Performance Optimization", "description": "Optimize your .NET MAUI apps for better performance and user experience. Learn about memory management, rendering optimization, profiling tools, and best practices.", "level": "advanced", "duration": "120 min", "estimatedTime": 120, "image": "assets/tutorials/performance.jpg", "videoUrl": "https://example.com/videos/performance-maui", "codeUrl": "https://github.com/dotnetwithmahesh/performance-maui", "premium": true, "category": "performance", "tags": ["performance", "optimization", "profiling", "memory", "rendering"], "publishDate": "2024-02-10T10:00:00Z", "lastUpdated": "2024-02-15T14:45:00Z", "views": 650, "rating": 4.9, "ratingCount": 76, "completions": 380, "difficulty": 5, "prerequisites": ["Custom Controls and Renderers", "Navigation and Shell"], "learningObjectives": ["Identify performance bottlenecks", "Optimize memory usage", "Improve rendering performance", "Use profiling tools effectively", "Implement performance best practices"], "chapters": [{"id": 1, "title": "Performance Fundamentals", "duration": "25 min", "videoUrl": "https://example.com/videos/performance-chapter1"}, {"id": 2, "title": "Memory Management", "duration": "30 min", "videoUrl": "https://example.com/videos/performance-chapter2"}, {"id": 3, "title": "Rendering Optimization", "duration": "35 min", "videoUrl": "https://example.com/videos/performance-chapter3"}, {"id": 4, "title": "Profiling and Debugging", "duration": "30 min", "videoUrl": "https://example.com/videos/performance-chapter4"}]}], "categories": [{"id": "basics", "name": "Basics", "description": "Fundamental concepts and getting started tutorials", "icon": "fas fa-play-circle", "color": "#10B981"}, {"id": "architecture", "name": "Architecture", "description": "Design patterns and architectural concepts", "icon": "fas fa-sitemap", "color": "#3B82F6"}, {"id": "ui", "name": "User Interface", "description": "UI controls, styling, and custom components", "icon": "fas fa-palette", "color": "#8B5CF6"}, {"id": "navigation", "name": "Navigation", "description": "App navigation and routing patterns", "icon": "fas fa-route", "color": "#F59E0B"}, {"id": "performance", "name": "Performance", "description": "Optimization and performance tuning", "icon": "fas fa-tachometer-alt", "color": "#EF4444"}, {"id": "data", "name": "Data & APIs", "description": "Data access, APIs, and storage solutions", "icon": "fas fa-database", "color": "#06B6D4"}], "levels": [{"id": "beginner", "name": "<PERSON><PERSON><PERSON>", "description": "Perfect for those new to .NET MAUI", "color": "#10B981", "difficulty": 1}, {"id": "intermediate", "name": "Intermediate", "description": "For developers with basic MAUI knowledge", "color": "#F59E0B", "difficulty": 3}, {"id": "advanced", "name": "Advanced", "description": "Complex topics for experienced developers", "color": "#EF4444", "difficulty": 5}]}