<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Learn .NET MAUI development with comprehensive tutorials, guides, and resources. Master cross-platform mobile and desktop app development.">
    <meta name="keywords" content=".NET MAUI, cross-platform development, mobile apps, desktop apps, C#, XAML, tutorials">
    <meta name="author" content="Mahesh Gadhave">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content=".NET with Mahesh - Master .NET MAUI Development">
    <meta property="og:description" content="Comprehensive .NET MAUI tutorials and resources for cross-platform app development">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://dotnetwithmahesh.dev">
    <meta property="og:image" content="https://dotnetwithmahesh.dev/assets/og-image.jpg">
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content=".NET with Mahesh - Master .NET MAUI Development">
    <meta name="twitter:description" content="Comprehensive .NET MAUI tutorials and resources for cross-platform app development">
    <meta name="twitter:image" content="https://dotnetwithmahesh.dev/assets/twitter-card.jpg">
    
    <title>.NET with Mahesh - Master .NET MAUI Development</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="assets/favicon.ico">
    <link rel="apple-touch-icon" sizes="180x180" href="assets/apple-touch-icon.png">
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/components.css">
    <link rel="stylesheet" href="styles/responsive.css">
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500&display=swap" rel="stylesheet">
    
    <!-- Font Awesome for Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "EducationalOrganization",
        "name": ".NET with Mahesh",
        "description": "Learn .NET MAUI development with comprehensive tutorials and resources",
        "url": "https://dotnetwithmahesh.dev",
        "founder": {
            "@type": "Person",
            "name": "Mahesh Gadhave",
            "url": "https://www.linkedin.com/in/mahesh-gadhave-xamarin-maui-developer/"
        }
    }
    </script>
</head>
<body>
    <!-- Navigation Header -->
    <header class="header">
        <nav class="navbar">
            <div class="nav-container">
                <div class="nav-brand">
                    <a href="index.html" class="brand-link">
                        <img src="assets/logo.svg" alt=".NET with Mahesh Logo" class="brand-logo">
                        <span class="brand-text">.NET with Mahesh</span>
                    </a>
                </div>
                
                <div class="nav-menu" id="navMenu">
                    <ul class="nav-list">
                        <li class="nav-item">
                            <a href="index.html" class="nav-link active">Home</a>
                        </li>
                        <li class="nav-item">
                            <a href="tutorials.html" class="nav-link">Tutorials</a>
                        </li>
                        <li class="nav-item">
                            <a href="blog.html" class="nav-link">Blog</a>
                        </li>
                        <li class="nav-item">
                            <a href="resources.html" class="nav-link">Resources</a>
                        </li>
                        <li class="nav-item">
                            <a href="contact.html" class="nav-link">Contact</a>
                        </li>
                    </ul>
                </div>
                
                <div class="nav-actions">
                    <button class="search-btn" id="searchBtn" aria-label="Search">
                        <i class="fas fa-search"></i>
                    </button>
                    <button class="theme-toggle" id="themeToggle" aria-label="Toggle theme">
                        <i class="fas fa-moon"></i>
                    </button>
                    <div class="auth-buttons">
                        <a href="login.html" class="btn btn-outline">Login</a>
                        <a href="register.html" class="btn btn-primary">Get Started</a>
                    </div>
                </div>
                
                <button class="mobile-menu-toggle" id="mobileMenuToggle" aria-label="Toggle mobile menu">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>
            </div>
        </nav>
        
        <!-- Search Overlay -->
        <div class="search-overlay" id="searchOverlay">
            <div class="search-container">
                <div class="search-box">
                    <input type="text" class="search-input" placeholder="Search tutorials, topics, or keywords..." id="searchInput">
                    <button class="search-submit" id="searchSubmit">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
                <button class="search-close" id="searchClose">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="search-results" id="searchResults"></div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Hero Section -->
        <section class="hero">
            <div class="hero-container">
                <div class="hero-content">
                    <div class="hero-text">
                        <h1 class="hero-title">
                            Master <span class="highlight">.NET MAUI</span> Development
                        </h1>
                        <p class="hero-description">
                            Learn to build stunning cross-platform mobile and desktop applications with .NET MAUI. 
                            From beginner basics to advanced techniques, get hands-on experience with comprehensive tutorials and real-world projects.
                        </p>
                        <div class="hero-stats">
                            <div class="stat">
                                <span class="stat-number">50+</span>
                                <span class="stat-label">Tutorials</span>
                            </div>
                            <div class="stat">
                                <span class="stat-number">1000+</span>
                                <span class="stat-label">Students</span>
                            </div>
                            <div class="stat">
                                <span class="stat-number">25+</span>
                                <span class="stat-label">Projects</span>
                            </div>
                        </div>
                        <div class="hero-actions">
                            <a href="tutorials.html" class="btn btn-primary btn-large">
                                <i class="fas fa-play"></i>
                                Start Learning
                            </a>
                            <a href="#featured-tutorials" class="btn btn-outline btn-large">
                                <i class="fas fa-book"></i>
                                Browse Tutorials
                            </a>
                        </div>
                    </div>
                    <div class="hero-visual">
                        <div class="hero-image">
                            <img src="assets/hero-maui-development.svg" alt=".NET MAUI Development Illustration" class="hero-img">
                        </div>
                        <div class="floating-elements">
                            <div class="floating-card card-1">
                                <i class="fab fa-android"></i>
                                <span>Android</span>
                            </div>
                            <div class="floating-card card-2">
                                <i class="fab fa-apple"></i>
                                <span>iOS</span>
                            </div>
                            <div class="floating-card card-3">
                                <i class="fab fa-windows"></i>
                                <span>Windows</span>
                            </div>
                            <div class="floating-card card-4">
                                <i class="fab fa-apple"></i>
                                <span>macOS</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Featured Tutorials Section -->
        <section class="featured-tutorials" id="featured-tutorials">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">Featured Tutorials</h2>
                    <p class="section-description">
                        Start your .NET MAUI journey with our most popular and comprehensive tutorials
                    </p>
                </div>
                
                <div class="tutorial-filters">
                    <button class="filter-btn active" data-filter="all">All</button>
                    <button class="filter-btn" data-filter="beginner">Beginner</button>
                    <button class="filter-btn" data-filter="intermediate">Intermediate</button>
                    <button class="filter-btn" data-filter="advanced">Advanced</button>
                </div>
                
                <div class="tutorial-grid" id="tutorialGrid">
                    <!-- Tutorial cards will be dynamically loaded here -->
                </div>
                
                <div class="section-footer">
                    <a href="tutorials.html" class="btn btn-outline">
                        View All Tutorials
                        <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            </div>
        </section>

        <!-- Learning Path Section -->
        <section class="learning-paths">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">Learning Paths</h2>
                    <p class="section-description">
                        Structured learning journeys to master .NET MAUI development
                    </p>
                </div>
                
                <div class="paths-grid">
                    <div class="path-card">
                        <div class="path-icon">
                            <i class="fas fa-rocket"></i>
                        </div>
                        <h3 class="path-title">Getting Started</h3>
                        <p class="path-description">
                            Perfect for beginners. Learn the fundamentals of .NET MAUI development.
                        </p>
                        <div class="path-meta">
                            <span class="path-duration">
                                <i class="fas fa-clock"></i>
                                4 weeks
                            </span>
                            <span class="path-lessons">
                                <i class="fas fa-book"></i>
                                12 lessons
                            </span>
                        </div>
                        <a href="learning-path-beginner.html" class="path-link">Start Path</a>
                    </div>
                    
                    <div class="path-card featured">
                        <div class="path-badge">Most Popular</div>
                        <div class="path-icon">
                            <i class="fas fa-code"></i>
                        </div>
                        <h3 class="path-title">Building Real Apps</h3>
                        <p class="path-description">
                            Build complete applications with advanced features and best practices.
                        </p>
                        <div class="path-meta">
                            <span class="path-duration">
                                <i class="fas fa-clock"></i>
                                8 weeks
                            </span>
                            <span class="path-lessons">
                                <i class="fas fa-book"></i>
                                24 lessons
                            </span>
                        </div>
                        <a href="learning-path-intermediate.html" class="path-link">Start Path</a>
                    </div>
                    
                    <div class="path-card">
                        <div class="path-icon">
                            <i class="fas fa-cogs"></i>
                        </div>
                        <h3 class="path-title">Advanced Techniques</h3>
                        <p class="path-description">
                            Master advanced patterns, performance optimization, and enterprise features.
                        </p>
                        <div class="path-meta">
                            <span class="path-duration">
                                <i class="fas fa-clock"></i>
                                6 weeks
                            </span>
                            <span class="path-lessons">
                                <i class="fas fa-book"></i>
                                18 lessons
                            </span>
                        </div>
                        <a href="learning-path-advanced.html" class="path-link">Start Path</a>
                    </div>
                </div>
            </div>
        </section>

        <!-- Newsletter Section -->
        <section class="newsletter">
            <div class="container">
                <div class="newsletter-content">
                    <div class="newsletter-text">
                        <h2 class="newsletter-title">Stay Updated</h2>
                        <p class="newsletter-description">
                            Get the latest .NET MAUI tutorials, tips, and updates delivered to your inbox.
                        </p>
                    </div>
                    <form class="newsletter-form" id="newsletterForm">
                        <div class="form-group">
                            <input type="email" class="form-input" placeholder="Enter your email address" required id="newsletterEmail">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-paper-plane"></i>
                                Subscribe
                            </button>
                        </div>
                        <p class="newsletter-privacy">
                            <i class="fas fa-shield-alt"></i>
                            We respect your privacy. Unsubscribe at any time.
                        </p>
                    </form>
                </div>
            </div>
        </section>

        <!-- About Section -->
        <section class="about-preview">
            <div class="container">
                <div class="about-content">
                    <div class="about-text">
                        <h2 class="about-title">About Mahesh Gadhave</h2>
                        <p class="about-description">
                            Passionate .NET MAUI developer with years of experience in cross-platform mobile and desktop application development.
                            I'm dedicated to sharing knowledge and helping developers master the art of building beautiful, performant applications with .NET MAUI.
                        </p>
                        <div class="about-highlights">
                            <div class="highlight-item">
                                <i class="fas fa-award"></i>
                                <span>Microsoft MVP</span>
                            </div>
                            <div class="highlight-item">
                                <i class="fas fa-users"></i>
                                <span>Community Leader</span>
                            </div>
                            <div class="highlight-item">
                                <i class="fas fa-code"></i>
                                <span>Open Source Contributor</span>
                            </div>
                        </div>
                        <div class="about-actions">
                            <a href="contact.html" class="btn btn-primary">Get in Touch</a>
                            <a href="https://www.linkedin.com/in/mahesh-gadhave-xamarin-maui-developer/" target="_blank" rel="noopener" class="btn btn-outline">
                                <i class="fab fa-linkedin"></i>
                                LinkedIn
                            </a>
                        </div>
                    </div>
                    <div class="about-image">
                        <img src="assets/mahesh-profile.jpg" alt="Mahesh Gadhave - .NET MAUI Developer" class="profile-img">
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <div class="footer-brand">
                        <img src="assets/logo.svg" alt=".NET with Mahesh Logo" class="footer-logo">
                        <h3 class="footer-brand-text">.NET with Mahesh</h3>
                        <p class="footer-brand-description">
                            Master .NET MAUI development with comprehensive tutorials and hands-on projects.
                        </p>
                    </div>
                </div>

                <div class="footer-section">
                    <h4 class="footer-title">Learning</h4>
                    <ul class="footer-links">
                        <li><a href="tutorials.html">All Tutorials</a></li>
                        <li><a href="learning-path-beginner.html">Beginner Path</a></li>
                        <li><a href="learning-path-intermediate.html">Intermediate Path</a></li>
                        <li><a href="learning-path-advanced.html">Advanced Path</a></li>
                        <li><a href="resources.html">Resources</a></li>
                    </ul>
                </div>

                <div class="footer-section">
                    <h4 class="footer-title">Content</h4>
                    <ul class="footer-links">
                        <li><a href="blog.html">Blog Posts</a></li>
                        <li><a href="code-samples.html">Code Samples</a></li>
                        <li><a href="presentations.html">Presentations</a></li>
                        <li><a href="projects.html">Sample Projects</a></li>
                    </ul>
                </div>

                <div class="footer-section">
                    <h4 class="footer-title">Connect</h4>
                    <ul class="footer-links">
                        <li><a href="contact.html">Contact</a></li>
                        <li><a href="https://www.linkedin.com/in/mahesh-gadhave-xamarin-maui-developer/" target="_blank" rel="noopener">LinkedIn</a></li>
                        <li><a href="https://github.com/maheshgadhave" target="_blank" rel="noopener">GitHub</a></li>
                        <li><a href="mailto:<EMAIL>">Email</a></li>
                    </ul>
                </div>

                <div class="footer-section">
                    <h4 class="footer-title">Legal</h4>
                    <ul class="footer-links">
                        <li><a href="privacy-policy.html">Privacy Policy</a></li>
                        <li><a href="terms-of-service.html">Terms of Service</a></li>
                        <li><a href="cookie-policy.html">Cookie Policy</a></li>
                    </ul>
                </div>
            </div>

            <div class="footer-bottom">
                <div class="footer-copyright">
                    <p>&copy; 2024 .NET with Mahesh. All rights reserved.</p>
                </div>
                <div class="footer-social">
                    <a href="https://www.linkedin.com/in/mahesh-gadhave-xamarin-maui-developer/" target="_blank" rel="noopener" class="social-link">
                        <i class="fab fa-linkedin"></i>
                    </a>
                    <a href="https://github.com/maheshgadhave" target="_blank" rel="noopener" class="social-link">
                        <i class="fab fa-github"></i>
                    </a>
                    <a href="https://twitter.com/maheshgadhave" target="_blank" rel="noopener" class="social-link">
                        <i class="fab fa-twitter"></i>
                    </a>
                    <a href="mailto:<EMAIL>" class="social-link">
                        <i class="fas fa-envelope"></i>
                    </a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="js/utils.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/search.js"></script>
    <script src="js/tutorials.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
