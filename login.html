<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Login to .NET with <PERSON><PERSON>h to access premium tutorials, track your progress, and unlock exclusive content.">
    <meta name="keywords" content="login, .NET MAUI, tutorials, premium content, learning platform">
    <meta name="author" content="Mahesh Gadhave">
    
    <title>Login - .NET with Mahesh</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="assets/favicon.ico">
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/components.css">
    <link rel="stylesheet" href="styles/responsive.css">
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <!-- Navigation Header -->
    <header class="header">
        <nav class="navbar">
            <div class="nav-container">
                <div class="nav-brand">
                    <a href="index.html" class="brand-link">
                        <img src="assets/logo.svg" alt=".NET with Mahesh Logo" class="brand-logo">
                        <span class="brand-text">.NET with Mahesh</span>
                    </a>
                </div>
                
                <div class="nav-actions">
                    <button class="theme-toggle" id="themeToggle" aria-label="Toggle theme">
                        <i class="fas fa-moon"></i>
                    </button>
                    <a href="register.html" class="btn btn-outline">Sign Up</a>
                </div>
            </div>
        </nav>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <section class="auth-section">
            <div class="auth-container">
                <div class="auth-grid">
                    <!-- Login Form -->
                    <div class="auth-form-container">
                        <div class="auth-header">
                            <h1 class="auth-title">Welcome Back</h1>
                            <p class="auth-description">
                                Sign in to your account to access premium tutorials and track your learning progress.
                            </p>
                        </div>

                        <form class="auth-form" id="loginForm">
                            <div class="form-group">
                                <label for="email" class="form-label">Email Address</label>
                                <input type="email" id="email" name="email" class="form-input" required 
                                       placeholder="Enter your email address">
                            </div>

                            <div class="form-group">
                                <label for="password" class="form-label">Password</label>
                                <div class="password-input-container">
                                    <input type="password" id="password" name="password" class="form-input" required 
                                           placeholder="Enter your password">
                                    <button type="button" class="password-toggle" id="passwordToggle">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </div>

                            <div class="form-options">
                                <label class="checkbox-label">
                                    <input type="checkbox" name="rememberMe" class="form-checkbox">
                                    <span class="checkbox-text">Remember me</span>
                                </label>
                                <a href="forgot-password.html" class="forgot-password-link">Forgot password?</a>
                            </div>

                            <button type="submit" class="btn btn-primary btn-large auth-submit">
                                <i class="fas fa-sign-in-alt"></i>
                                Sign In
                            </button>
                        </form>

                        <!-- Demo Account Info -->
                        <div class="demo-info">
                            <h3 class="demo-title">Demo Account</h3>
                            <p class="demo-description">Try the platform with our demo account:</p>
                            <div class="demo-credentials">
                                <div class="demo-credential">
                                    <strong>Email:</strong> <EMAIL>
                                </div>
                                <div class="demo-credential">
                                    <strong>Password:</strong> demo123
                                </div>
                            </div>
                            <button type="button" class="btn btn-outline btn-small" id="fillDemoCredentials">
                                Use Demo Account
                            </button>
                        </div>

                        <!-- Social Login -->
                        <div class="social-login">
                            <div class="social-divider">
                                <span class="social-divider-text">Or continue with</span>
                            </div>
                            <div class="social-buttons">
                                <button type="button" class="social-btn google-btn">
                                    <i class="fab fa-google"></i>
                                    Google
                                </button>
                                <button type="button" class="social-btn github-btn">
                                    <i class="fab fa-github"></i>
                                    GitHub
                                </button>
                                <button type="button" class="social-btn linkedin-btn">
                                    <i class="fab fa-linkedin"></i>
                                    LinkedIn
                                </button>
                            </div>
                        </div>

                        <!-- Sign Up Link -->
                        <div class="auth-footer">
                            <p class="auth-footer-text">
                                Don't have an account? 
                                <a href="register.html" class="auth-link">Sign up for free</a>
                            </p>
                        </div>
                    </div>

                    <!-- Benefits Sidebar -->
                    <div class="auth-benefits">
                        <div class="benefits-content">
                            <h2 class="benefits-title">Why Join .NET with Mahesh?</h2>
                            
                            <div class="benefit-item">
                                <div class="benefit-icon">
                                    <i class="fas fa-crown"></i>
                                </div>
                                <div class="benefit-content">
                                    <h3 class="benefit-title">Premium Tutorials</h3>
                                    <p class="benefit-description">
                                        Access exclusive, in-depth tutorials covering advanced .NET MAUI topics.
                                    </p>
                                </div>
                            </div>

                            <div class="benefit-item">
                                <div class="benefit-icon">
                                    <i class="fas fa-chart-line"></i>
                                </div>
                                <div class="benefit-content">
                                    <h3 class="benefit-title">Progress Tracking</h3>
                                    <p class="benefit-description">
                                        Track your learning progress and see how far you've come.
                                    </p>
                                </div>
                            </div>

                            <div class="benefit-item">
                                <div class="benefit-icon">
                                    <i class="fas fa-bookmark"></i>
                                </div>
                                <div class="benefit-content">
                                    <h3 class="benefit-title">Bookmarks & Notes</h3>
                                    <p class="benefit-description">
                                        Save your favorite tutorials and take notes for future reference.
                                    </p>
                                </div>
                            </div>

                            <div class="benefit-item">
                                <div class="benefit-icon">
                                    <i class="fas fa-certificate"></i>
                                </div>
                                <div class="benefit-content">
                                    <h3 class="benefit-title">Certificates</h3>
                                    <p class="benefit-description">
                                        Earn certificates upon completing learning paths and courses.
                                    </p>
                                </div>
                            </div>

                            <div class="benefit-item">
                                <div class="benefit-icon">
                                    <i class="fas fa-users"></i>
                                </div>
                                <div class="benefit-content">
                                    <h3 class="benefit-title">Community Access</h3>
                                    <p class="benefit-description">
                                        Join our community of .NET MAUI developers and get support.
                                    </p>
                                </div>
                            </div>

                            <div class="benefit-item">
                                <div class="benefit-icon">
                                    <i class="fas fa-download"></i>
                                </div>
                                <div class="benefit-content">
                                    <h3 class="benefit-title">Downloadable Resources</h3>
                                    <p class="benefit-description">
                                        Access code samples, templates, and other downloadable resources.
                                    </p>
                                </div>
                            </div>
                        </div>

                        <!-- Testimonial -->
                        <div class="testimonial">
                            <div class="testimonial-content">
                                <div class="testimonial-quote">
                                    <i class="fas fa-quote-left"></i>
                                </div>
                                <p class="testimonial-text">
                                    "The tutorials are incredibly detailed and practical. I went from zero to building 
                                    production-ready MAUI apps in just a few months!"
                                </p>
                                <div class="testimonial-author">
                                    <img src="assets/testimonials/sarah-johnson.jpg" alt="Sarah Johnson" class="testimonial-avatar">
                                    <div class="testimonial-info">
                                        <div class="testimonial-name">Sarah Johnson</div>
                                        <div class="testimonial-role">Mobile Developer</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-bottom">
                <div class="footer-copyright">
                    <p>&copy; 2024 .NET with Mahesh. All rights reserved.</p>
                </div>
                <div class="footer-links">
                    <a href="privacy-policy.html">Privacy Policy</a>
                    <a href="terms-of-service.html">Terms of Service</a>
                    <a href="contact.html">Contact</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="js/utils.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/login.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
